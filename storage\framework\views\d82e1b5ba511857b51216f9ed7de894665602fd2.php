<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<head>
    <?php echo $__env->make('layouts.title-meta', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <title>User Management | CK Projects</title>
    <?php echo $__env->make('layouts.head-css', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- Example using CDN -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.1.0/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"><script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.1.0/js/select2.min.js"></script>

</head>
<?php echo $__env->make('layouts.body', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<style>
    .jconfirm.jconfirm-white .jconfirm-box,
    .jconfirm.jconfirm-light .jconfirm-box {
        width: 100%;
    }
    .table-filter-container {
        text-align: right;
    }
    .font-weight-bold
    {
        font-weight:500;
    }
    #learning_devlopment_idea_table_processing{
        top: 19% !important;
    }
    .modal-footer .note {
        color:red;
    text-align: left;
    font-size: 12px;
    margin-bottom: 7px;
    margin-right: auto;
}

</style>



<input type="hidden" name="_token" id="csrf_token" value="<?php echo csrf_token(); ?>">

<div class="holder">

    <div class="wrapper">

        <?php echo $__env->make('layouts.topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- model starts -->

       
       
    <!-- View of learning development ideas modal Start -->
    <input id="userid" type="hidden" value="<?php echo e(auth()->id()); ?>">
    <input id="tab_value" type="hidden" value="1">
        <input id="roleuser" type="hidden" value="<?php echo e(Auth::user()->role); ?>">

    <div class="modal fade" id="viewlearningideatasks" tabindex="-1"
                role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl" role="document">
                    <div class="modal-content ">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel">View Ideas<span
                                    class="badge badge-soft-info font-size-12"></span></h5>
                            <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>

                        </div>
                        <!-- <form id="update_learning_devlopment_idea"> -->
                        <div class="modal-body">
                            <div id="show_learning_developement_idea">

                            </div>
                            <!-- <div class="col-6">
                                <div class="mb-3">
                                    <label class="form-label">Idea Comments</label>
                                    <input type="text" class="form-control" name="update_ideacommnets"
                                        
                                        style="width:100%;" />
                                </div>
                            </div> -->
                        </div>
                        <div class="modal-footer">
                            <!-- <button class="btn btn-success float-right" type="submit">Update</button> -->
                        </div>
                        <!-- </form> -->
                    </div>
                </div>
            </div>
            <!-- View of learning development ideas  modal Ends -->

        <!-- model ends -->

     
        <!-- model starts -->

        
        <?php if (isset($component)) { $__componentOriginal97e3bfe0078ad0fd8bf6528c0c9c69d8dcd7ab94 = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\Modal\IiyCreateModal::class, ['parentidea' => $parent_idea,'iiydigitallearning' => $iiy_digital_learning]); ?>
<?php $component->withName('modal.iiy-create-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal97e3bfe0078ad0fd8bf6528c0c9c69d8dcd7ab94)): ?>
<?php $component = $__componentOriginal97e3bfe0078ad0fd8bf6528c0c9c69d8dcd7ab94; ?>
<?php unset($__componentOriginal97e3bfe0078ad0fd8bf6528c0c9c69d8dcd7ab94); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal0d3191ae5ea916807e63309afddbf34731a2d607 = $component; } ?>
<?php $component = $__env->getContainer()->make(App\View\Components\Modal\InsightsCreateModal::class, ['parentidea' => $parent_idea,'insightcategoryhr' => $tbl_insight_category_hr,'scientist' => $scientist,'topiclearnt' => $topic_learnt]); ?>
<?php $component->withName('modal.insights-create-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0d3191ae5ea916807e63309afddbf34731a2d607)): ?>
<?php $component = $__componentOriginal0d3191ae5ea916807e63309afddbf34731a2d607; ?>
<?php unset($__componentOriginal0d3191ae5ea916807e63309afddbf34731a2d607); ?>
<?php endif; ?>
        
        
        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.modal.iiy-edit-modal','data' => ['parentidea' => $parent_idea,'iiydigitallearning' => $iiy_digital_learning]]); ?>
<?php $component->withName('modal.iiy-edit-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['parentidea' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($parent_idea),'iiydigitallearning' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($iiy_digital_learning)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.modal.iiy-view-modal','data' => ['parentidea' => $parent_idea,'iiydigitallearning' => $iiy_digital_learning]]); ?>
<?php $component->withName('modal.iiy-view-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['parentidea' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($parent_idea),'iiydigitallearning' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($iiy_digital_learning)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>


        
        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.modal.insights-edit-modal','data' => ['parentidea' => $parent_idea,'insightcategoryhr' => $tbl_insight_category_hr,'scientist' => $scientist,'topiclearnt' => $topic_learnt]]); ?>
<?php $component->withName('modal.insights-edit-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['parentidea' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($parent_idea),'insightcategoryhr' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($tbl_insight_category_hr),'scientist' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($scientist),'topiclearnt' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($topic_learnt)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.modal.idea-create-modal','data' => ['cocreatorlist' => $co_creator_list,'scientist' => $scientist,'ideatype' => $idea_type]]); ?>
<?php $component->withName('modal.idea-create-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['cocreatorlist' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($co_creator_list),'scientist' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($scientist),'ideatype' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($idea_type)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.modal.idea-edit-modal','data' => ['cocreatorlist' => $co_creator_list,'scientist' => $scientist,'ideatype' => $idea_type]]); ?>
<?php $component->withName('modal.idea-edit-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['cocreatorlist' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($co_creator_list),'scientist' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($scientist),'ideatype' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($idea_type)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>

       
        <!-- Edit learning development L&D modal Start -->

   
<!-- View of learning development L&D modal Ends -->
 <!-- history learning modal -->
 <div class="modal fade" id="historylearningideatasks" tabindex="-1" role="dialog"
            aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl" role="document"
                id="idea-modal">
                <div class="modal-content ">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Ideas History<span
                                class="badge badge-soft-info font-size-12"></span></h5>
                        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>

                    </div>
                    <!-- <form id="update_learning_devlopment_idea"> -->
                    <div class="modal-body">
                        <div id="history_learning_developement_idea">

                        </div>
                    </div>
                    <div class="modal-footer">
                        <!-- <button class="btn btn-success float-right" type="submit">Update</button> -->
                    </div>
                    <!-- </form> -->
                </div>
            </div>
        </div>
        <!-- history learning modal ends -->
        <!-- model ends -->

        <div class="content">
            <div class="container-fluid">
                <div class="content">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-12">
                                <div class="portlet">
                                    <div class="portlet-header portlet-header-bordered">
                                        <h3 class="portlet-title">Learning Development</h3>
                                        <div class="page-title-right">

                                        </div>
                                    </div>
                                    <div class="portlet-body">

                                        <div class="card">
                                            <div class="card-header">
                                                <div class="nav nav-tabs card-header-tabs" id="card3-tab">
                                                    <a class="nav-item nav-link active tab-nav" id="tab1-tab"  
                                                        data-bs-toggle="tab" href="#tab1">IIY</a>

                                                    <a class="nav-item nav-link tab-nav" id="tab0-tab" 
                                                       data-bs-toggle="tab" href="#tab0" 
                                                       <?php if(auth()->user()->role == 3 || auth()->user()->role == 5): ?>
                                                         style="display:block;"
                                                       <?php else: ?>
                                                         style="display:none;"
                                                       <?php endif; ?>
                                                    >
                                                      Evaluation
                                                    </a>
                                                    <a class="nav-item nav-link tab-nav" id="tab3-tab" data-bs-toggle="tab"
                                                        href="#tab3" >Insights </a>

                                                    <a class="nav-item nav-link tab-nav" id="tab2-tab" data-bs-toggle="tab"
                                                        href="#tab2" >Ideas</a>
                                                </div>
                                            </div>
                                            <div class="card-body" style="margin-top:20px;margin-bottom:20px;">
                                                <div class="tab-content">
                                                    <!-- tab1 -->
                                                    <div class="tab-pane fade show active" id="tab1">
                                                        <?php 
                                                            // $rowcount = DB::table('tbl_learning_devlopment')->where('created_by', auth()->id())->where('month', date('Y-m-01'))->where('deleted_status', 0)
                                                            // ->count();
                                                            // if($rowcount < 1)
                                                            // {
                                                        ?>
                                                        <button type="button"
                                                            class="btn btn-primary waves-effect waves-light"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#add_learning_devlopment_modal">Create
                                                        </button>
                                                        <?php  
                                                        // }
                                                        ?>
                                                        <div class="mt-3">
                                                            <?php $users = DB::table('users')
                                                                ->get()
                                                                ->toArray(); ?>
                                                            <div class="mt-6">
                                                                <div class="row">
                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3"> 
                                                                        <label class="form-label">Month</label>
                                                                        <input type="month" class="form-control"
                                                                            name="monthgoal" id="monthgoal"
                                                                            value="" min="2021-01">
                                                                    </div>

                                                                    <?php if((string) Auth::user()->role !== '2' && (string) Auth::user()->role !== '3'): ?>
                                                                    <!-- Show TL only if role is NOT 2 or 3 -->
                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                        <label class="form-label">TL</label>
                                                                        <select class="form-control select2" name="scientistfilter_cl"
                                                                            id="scientistfilter_cl" style="width:100%;">
                                                                        </select>
                                                                    </div>
                                                                    <?php endif; ?>

                                                                    <?php if(Auth::user()->role != 2): ?>
                                                                    <!-- Show Team Member if role is NOT 2 -->
                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                        <label class="form-label">TEAM MEMBER</label>
                                                                        <select class="form-control select2" name="scientistfilter"
                                                                            id="scientistfilter" style="width:100%;">
                                                                        </select>
                                                                    </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div><br>

                                                            <table id="learning_devlopment_table" class="table table-bordered table-striped table-hover" style="border:1px solid;">
                                                                <thead>
                                                                    <tr>                                                                      
                                                                        <th>S.NO</th>
                                                                        <th class="no-export">ACTIONS</th>
                                                                        <th>TOPIC LEARNT</th>
                                                                        <th>TOPICS PRESENTED</th>
                                                                        <th>Packaging Technologist</th>
                                                                        <th>HOURS INVESTED</th>
                                                                        <th>VENDOR FACILITY VISITED</th>
                                                                        <th>CONFERENCES ATTENDED</th>
                                                                        <th>EXHIBITIONS ATTENDED</th>
                                                                        <th>CONSUMER RETAIL VISITS</th>
                                                                        <th>IDEAS GENERATED</th>
                                                                        <th>DATE</th>                                                               
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                </tbody>
                                                            </table>
                                                           
                                                        </div>
                                                    </div>
                                                    <!-- tab1 ends-->
                                                    <!-- tab0 -->
                                                    <div class="tab-pane fade" id="tab0">
                                                        <div class="mt-3">
                                                            <div class="mt-6">
                                                                <div class="row">
                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                        <label class="form-label">Month</label>
                                                                        <input type="month" class="form-control"
                                                                            name="monthvalue" id="monthvalue"
                                                                            value="" min="2021-01">
                                                                    </div>
                                                                    <?php if((string) Auth::user()->role !== '2' && (string) Auth::user()->role !== '3'): ?>
                                                                    <!-- Show TL only if role is NOT 2 or 3 -->
                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                        <label class="form-label"> TL</label>
                                                                        <select class="form-select select2" name="scientistfilter_evaluation_cl"
                                                                            id="scientistfilter_evaluation_cl"
                                                                            style="width:100%;">
                                                                        </select>
                                                                    </div>
                                                                    <?php endif; ?>

                                                                    <?php if(Auth::user()->role != 2): ?>
                                                                    <!-- Show Team Member if role is NOT 2 -->
                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                        <label class="form-label"> TEAM MEMBER</label>
                                                                        <select class="form-control select2"  name="scientistfilter_evaluation"
                                                                            id="scientistfilter_evaluation"
                                                                            style="width:100%;">
                                                                        </select>
                                                                    </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div><br>
                                                            <table id="learning_devlopment_table_value" class="table table-bordered table-striped table-hover" style="border:1px solid;">
                                                                <thead>
                                                                <tr>
                                                                        <th>S.NO</th>
                                                                        <th class="no-export">ACTIONS</th>
                                                                        <th>TOPIC LEARNT</th>
                                                                        <th>TOPICS PRESENTED</th>
                                                                        <th>TEAM MEMBER NAME</th>
                                                                        <th>RATING</th>
                                                                        <th>FEEDBACK</th>
                                                                       <th>PRESENTATION</th>                                                                                                                                           </tr>
                                                                </thead>
                                                                <tbody>

                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>

                                                    <!-- tab2 -->
                                                    <div class="tab-pane fade" id="tab2">
                                                     
                                                        <button type="button"
                                                            class="btn btn-primary waves-effect waves-light"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#add_preferedconcepts_modal" id="create_btn_ideas" >Create
                                                        </button>
                                                         
                                                        <div class="mt-3">
                                                            <div class="mt-6">
                                                           
                                                                    <div class="row">

                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                            <label class="form-label">Month</label>
                                                                            <input type="month" class="form-control"
                                                                                name="month_idea" id="month_idea"
                                                                                value="">
                                                                        </div>
                                                                        <?php if(Auth::user()->role != 2): ?>
                                                                        <?php if(Auth::user()->role == 5 || Auth::user()->role == 4): ?>
                                                                        <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                            <label class="form-label">TL</label>
                                                                            <select class="form-control select2" name="scientistfilter_idea_cl"
                                                                                id="scientistfilter_idea_cl"
                                                                                style="width:100%;">
                                                                            </select>
                                                                        </div>
                                                                        <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                            <label class="form-label"> TEAM MEMBER</label>
                                                                            <select class="form-control select2"  name="scientistfilter_idea"
                                                                                id="scientistfilter_idea"
                                                                                style="width:100%;">
                                                                            </select>
                                                                        </div>
                                                                        <?php endif; ?>
                                                                        <?php if(Auth::user()->role == 6 || Auth::user()->role == 4): ?>
                                                                        <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                            <label class="form-label"> TL</label>
                                                                            <select class="form-control select2"  name="scientistfilter_idea"
                                                                                id="scientistfilter_idea"
                                                                                style="width:100%;">
                                                                            </select>
                                                                        </div>
                                                                        <?php endif; ?>
                                                                        <?php if(Auth::user()->role != 5): ?>
                                                                        <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3" id="scientistfilter_div">
                                                                            <label class="form-label">Team Member</label>
                                                                            <select class="form-control select2"
                                                                                name="scientistfilter_member_idea"
                                                                                id="scientistfilter_member_idea" style="width:100%;">
                                                                            </select>
                                                                        </div>
                                                                        <?php endif; ?>
                                                                        <?php endif; ?>
                                                                       
                                                                        <!-- <div class="col-2" style="padding-top: 21px;">
                                                                            <button type="button" name="filter" id="filterBtn" class="btn btn-primary">Filter</button>
                                                                        </div> -->
                                                                    </div>
                                                            </div><br>
                                                            <div class="row mb-3">
                                                            <div class="col-md-2">
                                                                <div class="p-3 text-white rounded shadow-sm" style="background-color: #01457a;">
                                                                    <h5 class="mb-0">Total Records: <span id="idea_count">0</span></h5>
                                                                </div>
                                                            </div>
                                                                
                                                                <div class="col-md-6"></div>

                                                                <?php if(Auth::user()->role != 2): ?>
                                                            <div class="col-md-4 d-flex ">      
                                                                <div class="p-3 bg-light border rounded shadow-sm " style="max-height: 200px; overflow-y: auto; width: 100%;">
                                                                    <h6 class="mb-2 text-dark ">Packaging Technologist-wise Records:</h6>
                                                                    <div id="idea_scientist_counts" class="text-dark small"></div>
                                                                </div>
                                                            </div>
                                                            <?php endif; ?>
                                                            </div>

                                                            <table id="learning_devlopment_idea_table"
                                                                class="table table-bordered table-striped table-hover">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Sl.no</th>
                                                                        <th class="no-export">Actions</th>
                                                                        <th>IDEA CATEGORY</th>
                                                                        <th>IDEA TYPE</th>
                                                                        <th>INSIGHTS</th>
                                                                        <th>CO Creator</th>
                                                                       <th>Packaging Technologist</th>
                                                                        <th>IDEA DESCRIPTION</th>
                                                                        <th>COMMENTS</th>
                                                                        <th>Target Date</th>
                                                                        <!-- <th>Idea status</th> -->
                                                                        <!-- <th>Date</th> -->
                                                                        
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <!-- tab2 ends-->
                                                    <!-- tab4 -->
                                                    <div class="tab-pane fade" id="tab4">
                                                    <div class="modal fade" id="viewModal" tabindex="-1" aria-labelledby="exampleModalLabel" data-bs-backdrop="static" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered modal-lg" style="max-width: 80%;">
                                                            <div class="modal-content">
                                                                <div class="portlet">
                                                                <div class="portlet-header portlet-header-bordered">
                                                                    <h1 class="portlet-title d-flex justify-content-center font-weight-bold" >PARENT IDEA STATUS</h1>
                                                                    <button type="button" class="btn btn-label-danger btn-icon" id="idea_status" data-bs-dismiss="modal"><i class="fa fa-times"></i></button>
                                                                    <div class="page-title-right"></div>
                                                                </div>
                                                                
                                                                    <div class="portlet-body">
                                                                        <div class="row">
                                                                            <div class="mt-3">
                                                                           
                                                                            <table id="learning_devlopment_idea_status"
                                                                                class="table table-bordered table-striped table-hover">
                                                                                <thead>
                                                                                    <tr >
                                                                                        <th>S.no</th>
                                                                                        <th>Parent Ideas Type</th>
                                                                                        <th>Ideas Generated</th>
                                                                                        <th>Ideas Status</th>
                                                                                        <th>Ideas weight</th>
                                                                                        <th>Created Date</th>
                                                                                    </tr>
                                                                                </thead>
                                                                                <tbody>

                                                                                </tbody>
                                                                            </table>
                                                                            <div class="row">
                                                                                
                                                                                <div class="col-1">
                                                                                    <div id="datatable_wrapper" class="dataTables_wrapper dt-bootstrap4 no-footer mt-4">
                                                                                        <div class="dataTables_length" id="datatable_length">
                                                                                            <label for="" class="show-entries-label">Show
                                                                                                <select name="datatable_length" id="showEntriesSelect" aria-controls="datatable" class="form-select form-select-sm">
                                                                                                    <option value="10">10</option>
                                                                                                    <option value="25">25</option>
                                                                                                    <option value="50">50</option>
                                                                                                    <option value="100">100</option>
                                                                                                </select>entries
                                                                                            </label>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-1">
                                                                                    <button id="loadMoreBtn" class="btn btn-primary mt-4" style=" width: 119px; left: 457px;">Load More</button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    </div>
                                                    </div>
                                                    <div class="tab-pane fade show " id="tab3">
                                                    <button type="button"
                                                        class="btn btn-primary waves-effect waves-light"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#add_insights_modal"
                                                        >Create
                                                    </button>

                                                        <div class="mt-3">
                                                            <?php $users = DB::table('users')
                                                                ->get()
                                                                ->toArray(); ?>
                                                            <div class="mt-6">


                                                                    <div class="row">
                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                            <label class="form-label">Month</label>
                                                                            <input type="month" class="form-control"
                                                                                name="month_insight" id="month_insight"
                                                                                value="">
                                                                        </div>
                                                                        
                                                                    <?php if(Auth::user()->role != 2): ?>
                                                                    <?php if(Auth::user()->role == 5 || Auth::user()->role == 4): ?>
                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                        <label class="form-label"> TL</label>
                                                                        <select class="form-control select2" name="scientistfilter_insight_cl"
                                                                            id="scientistfilter_insight_cl"
                                                                            style="width:100%;">
                                                                        </select>
                                                                    </div>
                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                        <label class="form-label"> TEAM MEMBER</label>
                                                                        <select class="form-control select2"  name="scientistfilter_insight"
                                                                            id="scientistfilter_insight"
                                                                            style="width:100%;">
                                                                        </select>
                                                                    </div>
                                                                    <?php endif; ?>
                                                                    <?php if(Auth::user()->role == 6 || Auth::user()->role == 4): ?>
                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3">
                                                                        <label class="form-label"> TL</label>
                                                                        <select class="form-control select2"  name="scientistfilter_insight"
                                                                            id="scientistfilter_insight"
                                                                            style="width:100%;">
                                                                        </select>
                                                                    </div>
                                                                    <?php endif; ?>
                                                                    <?php if(Auth::user()->role != 5): ?>
                                                                    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 mb-3" id="scientistfilter_div">
                                                                        <label class="form-label">Team Member</label>
                                                                        <select class="form-control select2"
                                                                            name="scientistfilter_member_insight"
                                                                            id="scientistfilter_member_insight" style="width:100%;">
                                                                        </select>
                                                                    </div>
                                                                    <?php endif; ?>
                                                                    </div>
                                                              <?php endif; ?>
                                                            </div><br>
                                                            <div class="row mb-3">
                                                            <div class="col-md-2">
                                                                <div class="p-3 text-white rounded shadow-sm" style="background-color: #01457a;">
                                                                    <h5 class="mb-0">Total Records: <span id="insight_count">0</span></h5>
                                                                </div>
                                                            </div>

                                                            <div class="col-md-6"></div>
                    
                                                            <?php if(Auth::user()->role != 2): ?>
                                                            <div class="col-md-4 d-flex ">
                                                                <div class="p-3 bg-light border rounded shadow-sm" style="max-height: 200px; overflow-y: auto; width: 100%;">
                                                                    <h6 class="mb-2 text-dark">Packaging Technologist-wise Records:</h6>
                                                                    <div id="scientist_counts" class="text-dark small"></div>
                                                                </div>
                                                            </div>
                                                            <?php endif; ?>
                                                        </div>
                                                               <table id="insights_devlopment_table"
                                                                class="table table-bordered table-striped table-hover"
                                                                >
                                                                <thead>
                                                                    
                                                                    <tr>
                                                                        <th>Sl.no</th>
                                                                        <th class="no-export">Action</th>
                                                                        <th>INSIGHTS GENERATED </th>
                                                                        <th>INSIGHTS CATEGORY</th>
                                                                        <th>Packaging Technologist</th>
                                                                        <th>REMARKS</th>
                                                                        <!-- <th>DATE</th> -->
                                                                    </tr>
                                                                </thead>
                                                            </table>
                                                        </div>
                                                    </div>
                                                     <!--tab5 starts-->
                                            <div class="tab-pane fade show active" id="tab5">
                                                <table id="insights_idea_gms" 
                                                    class="table table-bordered table-striped table-hover d-none"
                                                    style="border:1px solid;">
                                                    <thead>
                                                        <tr>
                                                            <th>S.no</th>
                                                            <th>innovation quotient 0.5((in_cnt*80)+(in_cnt*20))+0.2((idea_cnt*80)+(idea_cnt*20))+0.6((idea_exe_cnt*80)+idea_exe_cnt*20)+0.15((ipr_cnt*20+ipr_cnt*30+ipr_cnt*50))</th>
                                                            <!-- <th>100gms</th> -->
                                                        </tr>

                                                    </thead>
                                                    <tbody>

                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <!--tab5 ends-->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<?php echo $__env->make('layouts.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</div>



<div class="modal fade " id="add_percentage_modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-modal="true" >
    <div class="modal-dialog modal-md modal-scrollable">
        <div class="modal-content">
            <form id="percentage_form"  method="post" action="javascript:void(0)"> 
            <?php echo csrf_field(); ?>
            <div class="modal-header">
                <h5 class="modal-title">Add Percentage</h5>
                <button type="button" class="btn btn-label-danger btn-icon date_change " data-bs-dismiss="modal"><i class="fa fa-times"></i></button>
            </div>
            <div class="modal-body">
                
                    <input class="form-control" type="hidden" name="percentage_id" id="percentage_id">
                    <input class="form-control" type="hidden" name="percentage_key" id="percentage_key">
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 row" id="selectproject_typediv">
                            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6">
                                <div class="mb-3">
                                    <label class="form-label">Percentage</label>
                                    <input class="form-control" type="text" name="percentage" id="percentage" placeholder="Enter Percentage" onkeyup="validateScientistPercentage(this)" required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary waves-effect date_change form_reset" data-bs-dismiss="modal">Close</button>
                <button type="button" id="add_project_forms" class="btn btn-primary waves-effect waves-light" onclick="percentage_add()">Submit</button>
            </div>
            </form>
        </div>
    </div>

</div>
<?php echo $__env->make('layouts.theme_change', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('layouts.left_menu', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<?php echo $__env->make('layouts.vendor-scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<script type="text/javascript" src="<?php echo e(asset('app/pages/form/datepicker.js')); ?>"></script>
<script src="//cdn.rawgit.com/ashl1/datatables-rowsgroup/v2.0.0/dataTables.rowsGroup.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment/min/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

</body>

</html>
<script>
    let topic_learnt_arr = <?php echo json_encode($topic_learnt, 15, 512) ?>;
 

    $(document).on('click','#create_iiy_modal_submit_btn', (e) => {
        e.preventDefault();
        learning_development();
    });

   

    // function learning_development() {
    //     let fields = [
    //         { id: 'topics_learnt', message: "Please enter the topic of IIY" },
    //         { id: 'area_of_development', message: "Please select area of development" },
    //         { id: 'source_learning', message: "Please select source of learning" },
    //         { id: 'hours_spent', message: "Please enter hours spent" },
    //         { id: 'current_skill_level', message: "Please enter current skill level" },
    //         { id: 'iiy_remarks', message: "Please enter remarks" },
    //     ];

    //     let area_of_development_val = $('#area_of_development').val();

    //     if (area_of_development_val === '12') {
    //         fields.push({ id: 'project_iiy', message: 'Please enter the project name' });
    //     }

    //     if (area_of_development_val === '11') {
    //         fields.push({ id: 'other_iiy', message: 'Please enter the other input' });
    //     }

    //     let data = { _token: $('#csrf_token').val() };

    //     for (let field of fields) {
    //         let element = $('#' + field.id);
    //         let value = element.is('select') ? element.val() : element.val().trim();
    //         if (!value || value === "0") {
    //             $.alert({ title: 'Alert!', content: field.message });
    //             return;
    //         }
    //         data[field.id] = value;
    //     }

    //     // **Ensure `project_iiy` and `other_area_of_dev_input` are always sent in the AJAX request**
    //     data['project_iiy'] = $('#project_iiy').val() ? $('#project_iiy').val().trim() : '';
    //     data['other_iiy'] = $('#other_iiy').val() ? $('#other_iiy').val().trim() : '';

    //     $.ajax({
    //         type: "POST",
    //         url: "<?php echo e(url('save_learning_development')); ?>",
    //         dataType: "json",
    //         data: data,
    //         success: function (res) {
    //             if (res.success) {
    //                 $('add_learning_devlopment_modal').modal('hide');
    //                 Swal.fire({
    //                     position: 'top-end',
    //                     icon: 'success',
    //                     title: 'Created Successfully',
    //                     showConfirmButton: false,
    //                     customClass: 'swal-wide',
    //                     timer: 1500
    //                 });
    //                 $('#learning_devlopment_table').DataTable().ajax.reload();
    //             } else if (res.errors && res.errors.duplicate) {
    //                 Swal.fire({
    //                     icon: 'error',
    //                     title: 'Duplicate Entry',
    //                     text: res.errors.duplicate,
    //                 });
    //             } else {
    //                 $.alert({ title: 'Validation Error', content: Object.values(res.errors).join('<br>') });
    //             }
    //         }
    //     });

    // }
    
 document.getElementById('weekly_presentation').addEventListener('change', function () {
   let allowedExtensions = ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'doc']; // Add '.doc' to the array
    let files = this.files;

    for (let i = 0; i < files.length; i++) {
        let fileExtension = files[i].name.split('.').pop().toLowerCase();
        
        if (!allowedExtensions.includes(fileExtension)) {
            alert('Only PDF, DOC, DOCX, PPT, and PPTX files are allowed.');
            this.value = ''; // Clear the input
            return;
        }
    }
});

function learning_development() {
    let formData = new FormData();
    formData.append("_token", $('#csrf_token').val());

    let fields = [
        { id: 'topics_learnt', message: "Please enter the topic of IIY" },
        { id: 'topics_present', message: "Please enter the topic presented" },
        { id: 'hours_invested', message: "Please enter hours spent" },
        { id: 'vendor_facility_visited', message: "Please enter no of vendor visited" },
        { id: 'conferences_attended', message: "Please enter no of conference visited" },
        { id: 'exhibitions_attended', message: "Please enter no of exhibition visited" },
        { id: 'consumer_retail_visits', message: "Please enter no of consumer visited" },
        { id: 'ideas_generated', message: "Please enter ideas generated" },
        { id: 'date_range', message: "Please select the date range" }
        ];

    for (let field of fields) {
        let element = $('#' + field.id);
        let value = element.is('select') ? element.val() : element.val().trim();
        if (!value || value === null) {
            $.alert({ title: 'Alert!', content: field.message });
            return;
        }
        formData.append(field.id, value);
    }

    let fileInput = $('#weekly_presentation')[0];
    // if (fileInput.files.length === 0) {
    //     $.alert({ title: 'Alert!', content: "Please upload a presentation file." });
    //     return;
    // }

    for (let i = 0; i < fileInput.files.length; i++) {
        let file = fileInput.files[i];
        let allowedExtensions = ["pdf", "doc", "docx", "ppt", "pptx"];
        let fileExtension = file.name.split('.').pop().toLowerCase();

        if (!allowedExtensions.includes(fileExtension)) {
            $.alert({ title: 'Alert!', content: "Only PDF, DOC, DOCX, PPT, and PPTX files are allowed." });
            return;
        }

        formData.append('weekly_presentation[]', file); 
    }
let fullRange = $('#date_range').val();
let startDate, endDate;

if (fullRange.includes(' - ')) {
    [startDate, endDate] = fullRange.split(' - ');
} else if (fullRange.includes(' to ')) {
    [startDate, endDate] = fullRange.split(' to ');
}

if (!startDate || !endDate) {
    $.alert({ title: 'Alert!', content: 'Invalid date range format selected.' });
    return;
}

formData.append('date_range_start', startDate.trim());
formData.append('date_range_end', endDate.trim());


    $.ajax({
        type: "POST",
        url: "<?php echo e(url('save_learning_development')); ?>",
        dataType: "json",
        processData: false,
        contentType: false,
        data: formData,
        success: function (res) {
            if (res.success) {
                $('#add_learning_devlopment_modal').modal('hide');
                Swal.fire({
                    position: 'top-end',
                    icon: 'success',
                    title: 'Created Successfully',
                    showConfirmButton: false,
                    customClass: 'swal-wide',
                    timer: 1500
                });
                $('#learning_devlopment_table').DataTable().ajax.reload();
            } else if (res.errors) {
                if (res.errors.weekly_limit) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        text: res.errors.weekly_limit,
                    });
                } else if (res.errors.weekly_presentation) {
                    Swal.fire({
                        icon: 'error',
                        title: 'File Upload Error',
                        text: res.errors.weekly_presentation,
                    });
                } else {
                    $.alert({ title: 'Validation Error', content: Object.values(res.errors).join('<br>') });
                }
            }
        }
    });
}






    // Reset the modal form
    $("#add_learning_devlopment_modal").on("hidden.bs.modal", function () {
        $(this).find("input, textarea, select").val("").trigger("change");
    });


$(document).on('submit', '#learning_devlopment_insights', function (e) {
    e.preventDefault();


    let insightGenerated = $('#insight_generated').val();
    let insightCategory = $('#insight_category').val();
    let insightRemarks = $('#insight_remarks').val();
    let teamMember = $('#scientist_insights').val();

    // Validation
    if (!insightGenerated) {
        $.alert({ title: 'Invalid', content: 'Please enter insight generated', autoClose: 'logoutUser|300' });
        return;
    }
    if (!insightCategory) {
        $.alert({ title: 'Invalid', content: 'Please select insight category', autoClose: 'logoutUser|300' });
        return;
    }
    // if (!teamMember || teamMember === "0") {
    //     $.alert({ title: 'Invalid', content: 'Please select team member', autoClose: 'logoutUser|300' });
    //     return;
    // }
    // if (!insightRemarks) {
    //     $.alert({ title: 'Invalid', content: 'Please enter remarks', autoClose: 'logoutUser|300' });
    //     return;
    // }


    let scientistid = $('#roleuser').val() != 2 ? teamMember : $('#userid').val();
   


    let data = {
        "_token": "<?php echo e(csrf_token()); ?>",
        "insight_generated": insightGenerated,
        "insight_category": insightCategory,
        "insight_remarks": insightRemarks,
        "team_member": scientistid
       
    };

    $.ajax({
        type: "POST",
        url: "<?php echo e(url('save_learning_devlopment_insight')); ?>",
        data,
        success: function (res) {
            
            if (res.success) {
                Swal.fire({
                    position: 'top-end',
                    icon: 'success',
                    title: 'Created Successfully',
                    showConfirmButton: false,
                    customClass: 'swal-wide',
                    timer: 1500
                });
                $("#insights_devlopment_table").DataTable().ajax.reload();
                $('#add_insights_modal').modal('hide');
                resetInsightsForm(); 
            }
        }
    });
});





$('#add_preferedconcepts_modal').on('shown.bs.modal', function () {
    get_scientist_insight();  
});
// $('#add_preferedconcepts_modal').on('shown.bs.modal', function () {
//     get_ideas_learnt();
// })
$(document).on('hidden.bs.modal', '#add_preferedconcepts_modal', function () {
    $(this).find('form')[0].reset();

    $(this).find('.select2').val(null).trigger('change');

    $('#other_idea_div').addClass('d-none');
    $('#project_idea_div').addClass('d-none');

    $('#area_of_dev_idea').val("").trigger('change');
    $('#other_insight').val("");
    $('#project_insight').val("");
});

$(document).on('hidden.bs.modal', '#add_insights_modal', function () {
    $(this).find('form')[0].reset();
    $('#insight_category').val("").trigger('change');
});


    $('.tab-nav').on('click', function() {
      $('#learning_devlopment_table').DataTable().destroy();
      $('#insights_devlopment_table').DataTable().destroy();
      $('#learning_devlopment_idea_table').DataTable().destroy();
    //  $('#learning_devlopment_table_value').DataTable().destroy();

      get_learning_devlopment_list();
      get_learning_insight() ;
      get_learning_devlopment_idea_list();
    //  get_learning_development_evaluation_list();
    });
    
    $('.select2').select2();
   
    $("#idea_category").change(function() {
    if($("#idea_category").val()=="1")
    {   
        $(".iiy_form_field_hide").hide();     
        $(".product").show(); 
        $(".common").show();
        $(".brand_cover").show();
        
    }
    else if($("#idea_category").val()=="2")
    {
        $(".iiy_form_field_hide").hide();     
        $(".squees").show(); 
        $(".common").show();
        $(".brand_cover").show();

    }  else if($("#idea_category").val()=="3")
    {
        $(".iiy_form_field_hide").hide();     
        $(".method").show(); 
        $(".common").show(); 
        

    } else if($("#idea_category").val()=="4")
    {
        $(".iiy_form_field_hide").hide();     
        $(".research").show(); 
        $(".common").show();
    }

   
});
$(document).ready(function() {
    var activeTabIndex = localStorage.getItem('activeTabIndex');

    if (activeTabIndex) {
        setTimeout(() => {
            var tabElement = $("#" + activeTabIndex);
            if (tabElement.length && tabElement.is(':visible')) {
                tabElement[0].click();
            } else {
                if ($('#tab2-tab').is(':visible')) {
                    $('#tab2-tab')[0].click();
                    localStorage.setItem('activeTabIndex', 'tab2-tab');
                } else {
                    var firstVisibleTab = $('.nav-tabs a:visible:first').attr('id');
                    if (firstVisibleTab) {
                        $('#' + firstVisibleTab)[0].click();
                        localStorage.setItem('activeTabIndex', firstVisibleTab);
                    }
                }
            }
        }, 200); 
    }

    $('.nav-tabs a[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
        localStorage.setItem('activeTabIndex', e.target.id);
    });

    $(".functional, .learning_development").removeClass("active");
    $(".learning_development").addClass("active"); 

    var txtMonth = document.getElementById('month_idea');
    var date = new Date();
    var month = ("0" + (date.getMonth() + 1)).slice(-2);
    var yearMonth = date.getFullYear() + "-" + month;

    txtMonth.value = yearMonth;
    txtMonth.setAttribute('max', yearMonth);

    txtMonth.addEventListener('input', function(event) {
        var enteredValue = txtMonth.value;
        var enteredYear = parseInt(enteredValue.split('-')[0]);
        var currentYear = date.getFullYear();
        var currentMonth = date.getMonth() + 1;

        if (enteredYear > currentYear || (enteredYear === currentYear && parseInt(enteredValue.split('-')[1]) > currentMonth)) {
            txtMonth.value = yearMonth;
            event.preventDefault();
        }
    });

    $('#month_insight, #monthgoal, #monthvalue').val(yearMonth).attr('max', yearMonth);

    get_learning_devlopment_list();
    
    $('#learning_devlopment_table_value').DataTable().destroy();
    get_learning_development_evaluation_list();
});

    $('.alert-danger').delay(5000).fadeOut('slow');
    $('.alert-success').delay(5000).fadeOut('slow');



$('#learning_devlopment_idea').submit(function(e) {
    e.preventDefault();

    var scientist = $('#scientist').val();
    var idea_txt = $('#idea_txt').val();
    var idea_insight = $('#idea_insight').val();
    var idea_category = $('#idea_category option:selected').text(); 
    var idea_type = $('#idea_type option:selected').text(); 
    var ideas_comments = $('#ideas_comments').val();
    var target_date = $('#target_date').val();
   

    $.ajax({
        type: "POST",
        url: "<?php echo e(url('save_learning_devlopment_idea')); ?>",
        data: {
            "_token": "<?php echo e(csrf_token()); ?>",
            "scientist": scientist,
            "idea_txt": idea_txt,
            "idea_insight": idea_insight,
            "idea_category": idea_category,
            "idea_type": idea_type,
            "ideas_comments": ideas_comments,
            "target_date": target_date
        },
        success: function(data) {
            if (data == 1) {
                $('#add_preferedconcepts_modal').modal('hide');

                Swal.fire({
                    position: 'top-end',
                    icon: 'success',
                    title: 'Created Successfully',
                    showConfirmButton: false,
                    customClass: 'swal-wide',
                    timer: 1500
                });
                $("#learning_devlopment_idea_table").DataTable().ajax.reload();
            }
        }
    });
});

    
    $(".btn_close_ideas").click(function() {
        document.getElementById("learning_devlopment_idea").reset();
        $("#idea_cate").val("");
        $("#idea_cate").selectedIndex = 0;
        $("#idea_cate").trigger("change");
        $("#idea_insight").val("");
        $("#idea_insight").trigger("change");
        $('#idea_category').val("");
        $('#idea_category').trigger("change");
        $('#squeeze_idea').val("");
        $('#squeeze_idea').trigger("change");         
        $("#product_category").val("");
        $("#product_category").trigger("change");
        $("#product_brand").val("");
        $("#product_brand").trigger("change");
        $("#replicationcompetitors").val("");
        $("#replicationcompetitors").trigger("change");
        $("#publicationscope").val("");
        $("#publicationscope").trigger("change");
        $("#ideaweight").val("");
        $("#ideaweight").trigger("change");
        $("#squees_ideastatus_squees").val("");
        $("#squees_ideastatus_squees").trigger("change");
        $("#ideastatusproduct").val("");
        $("#ideastatusproduct").trigger("change");
        $("#method_ideastatus_squees").val("");
        $("#method_ideastatus_squees").trigger("change");
        $("#research_ideastatus_research").val("");
        $("#research_ideastatus_research").trigger("change");
        $(".iiy_form_field_hide").hide();
        //$("#idea_insight").select2("val", "");
    });

    var txtMonth = document.getElementById('monthgoal');
    var date = new Date();
    var month = "0" + (date.getMonth() + 1);
    txtMonth.value = (date.getFullYear() + "-" + (month.slice(-2)));
    txtMonth.addEventListener('input', function(event) {
        var enteredValue = txtMonth.value;
        var enteredYear = parseInt(enteredValue.split('-')[0]); 
        var currentYear = date.getFullYear();
        var currentMonth = date.getMonth() + 1; 
        if (enteredYear > currentYear || (enteredYear === currentYear && parseInt(enteredValue.split('-')[1]) > currentMonth)) {
            txtMonth.value = (currentYear + "-" + (month.slice(-2)));
            event.preventDefault();
        }
    });
    txtMonth.setAttribute('max', (date.getFullYear() + "-" + (month.slice(-2))));

$(document).on("click", ".edit-iiy-btn", function () {
    let iiyId = $(this).data("id");

    if (!iiyId) {
        console.error("Error: IIY ID is missing!");
        return;
    }

    $("#edit_iiy_id").val(iiyId);

    $.ajax({
        url: "<?php echo e(url('edit_learning_development')); ?>",
        type: "GET",
        dataType: "json",
        data: { id: iiyId },
        success: function (data) {
            if (!data) {
                console.error("Error: No data returned from the server.");
                return;
            }

            $('#edit_topics_learnt').val(data.topics_learnt);
            $('#edit_topics_present').val(data.topics_present);
            $('#edit_hours_invested').val(data.hours_invested);
            $('#edit_vendor_facility_visited').val(data.vendor_facility_visited);
            $('#edit_conferences_attended').val(data.conferences_attended);
            $('#edit_exhibitions_attended').val(data.exhibitions_attended);
            $('#edit_consumer_retail_visits').val(data.consumer_retail_visits);
            $('#edit_ideas_generated').val(data.ideas_generated);

            let fileContainer = $("#edit_weekly_presentation");
            fileContainer.empty();

            if (data.uploaded_files && data.uploaded_files.length > 0) {
                data.uploaded_files.forEach(filePath => {
                    let fileName = filePath.split('/').pop();
                    let truncatedFileName = fileName.length > 25 ? fileName.substring(0, 22) + "..." : fileName;
                    let fileExtension = fileName.split('.').pop().toLowerCase();
                    let fileURL = filePath;

                    let fileItem = `
                    <div class="d-flex align-items-center justify-content-between file-item" data-filename="${fileName}" style="margin-bottom: 5px;">
                        <a href="${fileURL}" class="file-link text-primary" data-bs-toggle="tooltip" 
                           data-bs-placement="top" title="${fileName}" 
                           ${fileExtension === "pdf" ? 'target="_blank"' : 'download'}>
                            ${truncatedFileName}
                        </a>
                        <button type="button" class="btn btn-danger btn-sm delete-file-btn" 
                                data-filename="${fileName}">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>`;

                    fileContainer.append(fileItem);
                });

                setTimeout(() => {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }, 500);
            } else {
                fileContainer.append("<span class='text-danger'>No file uploaded.</span>");
            }
            if (data.date_range_start && data.date_range_end) {
                const dateRange = `${data.date_range_start} to ${data.date_range_end}`;
                $('#edit_date_range').val(dateRange);
            }
            $("#edit_learning_development_modal").modal("show");
        },
        error: function (xhr, status, error) {
            console.error("AJAX Error:", error);
        }
    });
});

$('#uploaded_files').on('change', function (event) {
    let files = event.target.files;
    for (let i = 0; i < files.length; i++) {
        selectedFiles.push(files[i]); // Add new files
    }
});

$(document).on("click", ".delete-file-btn", function () {
    var fileName = $(this).data("filename");
    var iiyId = $('#edit_iiy_id').val();
    var _token = $('#csrf_token').val();
    var fileItem = $(this).closest('.file-item'); // Reference to file element

    Swal.fire({
        title: 'Are you sure?',
        text: `You are about to delete the file: ${fileName}`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Disable button to prevent multiple clicks
            $(this).prop("disabled", true);

            $.ajax({
                url: "<?php echo e(url('delete_file')); ?>",
                type: "POST",
                data: {
                    _token: _token,
                    file_name: fileName,
                    edit_iiy_id: iiyId
                },
                success: function (response) {
                    if (response.success) {
                        fileItem.fadeOut("slow", function () { 
                            $(this).remove();  // Remove from UI after fade out
                        });

                        Swal.fire({
                            position: 'top-end',
                            icon: 'success',
                            title: 'File deleted successfully!',
                            showConfirmButton: false,
                            timer: 1500
                        });

                        // Update hidden input with remaining file names
                        updateFileList();
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'File Deletion Failed',
                            text: response.message || 'The file could not be deleted.'
                        });

                        $(this).prop("disabled", false); // Re-enable button
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Delete error:", xhr.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error Occurred',
                        text: 'There was an error while deleting the file. Please try again.'
                    });

                    $(this).prop("disabled", false); // Re-enable button
                }
            });
        }
    });
});

// Function to update file list after deletion
function updateFileList() {
    let remainingFiles = [];
    $(".file-item").each(function () {
        remainingFiles.push($(this).data("filename"));
    });

    $("#filesToDelete").val(JSON.stringify(remainingFiles)); // Update hidden field
}





let selectedFiles = []; 

function handleFileSelect(event) {
    const fileList = document.getElementById("edit_weekly_presentation");
    fileList.innerHTML = ""; 

    selectedFiles = Array.from(event.target.files); 

    selectedFiles.forEach((file, index) => {
        let fileName = file.name;
        let fileExtension = fileName.split('.').pop().toLowerCase();

        let fileIcon = "fa-file-alt";
        if (fileExtension === "pdf") {
            fileIcon = "fa-file-pdf text-danger";
        } else if (["doc", "docx"].includes(fileExtension)) {
            fileIcon = "fa-file-word text-primary";
        } else if (["ppt", "pptx"].includes(fileExtension)) {
            fileIcon = "fa-file-powerpoint text-warning";
        }

        let listItem = document.createElement("li");
        listItem.className = "list-group-item d-flex align-items-center";
        listItem.style.cssText = "margin-bottom: 8px; margin-top: 10px; background-color: #d5f2ff; border-radius: 3px;";

        listItem.innerHTML = `
            <i class="fa ${fileIcon} me-2" style="font-size:18px"></i>  
            <span class="file-name">${fileName}</span>
            <button type="button" class="btn btn-danger btn-sm ms-auto delete-file" onclick="removeFile(${index})">
                <i class="fa fa-trash"></i>
            </button>
        `;

        fileList.appendChild(listItem);
    });
}

function removeFile(index) {
    selectedFiles.splice(index, 1);
    refreshFileList(); 
}

function refreshFileList() {
    const fileList = document.getElementById("edit_weekly_presentation");
    fileList.innerHTML = ""; 

    selectedFiles.forEach((file, index) => {
        let fileName = file.name;
        let fileExtension = fileName.split('.').pop().toLowerCase();

        let fileIcon = "fa-file-alt";
        if (fileExtension === "pdf") {
            fileIcon = "fa-file-pdf text-danger";
        } else if (["doc", "docx"].includes(fileExtension)) {
            fileIcon = "fa-file-word text-primary";
        } else if (["ppt", "pptx"].includes(fileExtension)) {
            fileIcon = "fa-file-powerpoint text-warning";
        }

        let listItem = document.createElement("li");
        listItem.className = "list-group-item d-flex align-items-center";
        listItem.style.cssText = "margin-bottom: 8px; margin-top: 10px; background-color: #d5f2ff; border-radius: 3px;";

        listItem.innerHTML = `
            <i class="fa ${fileIcon} me-2" style="font-size:18px"></i>  
            <span class="file-name">${fileName}</span>
            <button type="button" class="btn btn-danger btn-sm ms-auto delete-file" onclick="removeFile(${index})">
                <i class="fa fa-trash"></i>
            </button>
        `;

        fileList.appendChild(listItem);
    });
}



$('#edit_iiy_modal_submit_btn').click(function () {
    var formData = new FormData();
    var _token = $('#csrf_token').val();
    var edit_iiy_id = $('#edit_iiy_id').val();
    var topics_learnt = $('#edit_topics_learnt').val();
    var topics_present = $('#edit_topics_present').val();
    var hours_invested = $('#edit_hours_invested').val();
    var vendor_facility_visited = $('#edit_vendor_facility_visited').val();
    var conferences_attended = $('#edit_conferences_attended').val();
    var exhibitions_attended = $('#edit_exhibitions_attended').val();
    var consumer_retail_visits = $('#edit_consumer_retail_visits').val();
    var ideas_generated = $('#edit_ideas_generated').val();
    var filesToDelete = $('#filesToDelete').val() ? JSON.parse($('#filesToDelete').val()) : [];

    formData.append("_token", _token);
    formData.append("edit_iiy_id", edit_iiy_id);
    formData.append("topics_learnt", topics_learnt);
    formData.append("topics_present", topics_present);
    formData.append("hours_invested", hours_invested);
    formData.append("vendor_facility_visited", vendor_facility_visited);
    formData.append("conferences_attended", conferences_attended);
    formData.append("exhibitions_attended", exhibitions_attended);
    formData.append("consumer_retail_visits", consumer_retail_visits);
    formData.append("ideas_generated", ideas_generated);

    let fullEditDateRange = $('#edit_date_range').val();
let editStartDate, editEndDate;

if (fullEditDateRange.includes(' - ')) {
    [editStartDate, editEndDate] = fullEditDateRange.split(' - ');
} else if (fullEditDateRange.includes(' to ')) {
    [editStartDate, editEndDate] = fullEditDateRange.split(' to ');
}

if (!editStartDate || !editEndDate) {
    Swal.fire({
        icon: 'error',
        title: 'Invalid Date Range',
        text: 'Please select a valid date range.'
    });
    return;
}

formData.append('date_range_start', editStartDate.trim());
formData.append('date_range_end', editEndDate.trim());


    if (filesToDelete.length > 0) {
        formData.append("files_to_delete", JSON.stringify(filesToDelete));
    }

    selectedFiles.forEach(file => {
        formData.append("uploaded_files[]", file); 
    });

    $.ajax({
        type: "POST",
        url: "<?php echo e(url('update_iiy')); ?>",
        data: formData,
        processData: false, 
        contentType: false, 
        success: function (response) {
            if (response.success) {
                Swal.fire({
                    position: 'top-end',
                    icon: 'success',
                    title: 'Updated Successfully',
                    showConfirmButton: false,
                    timer: 1500
                });
                $('#edit_learning_development_modal').modal('hide');
                $("#learning_devlopment_table").DataTable().ajax.reload();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Update Failed',
                    text: 'Please check your input and try again.'
                });
            }
        },
        error: function (xhr) {
            Swal.fire({
                icon: 'error',
                title: 'Update Failed',
                text: 'An error occurred while updating. Please try again.'
            });
        }
    });
});



$('#view_iiy_modal_submit_btn').click(function () {
    var _token = $('#csrf_token').val();
    var iiyId = $('#title_id_val').val();

    var view_rating = $('#view_rating').val();
    var view_feedback = $('#view_feedback').val();

    $.ajax({
        type: "POST",
        url: "<?php echo e(url('view_iiy')); ?>",
        data: {
            _token: _token,
            view_iiy_id: iiyId,
            view_rating: view_rating,
            view_feedback: view_feedback
        },
        success: function (data) {
            if (data.success) {
                Swal.fire({
                    position: 'top-end',
                    icon: 'success',
                    title: 'Updated Successfully',
                    showConfirmButton: false,
                    customClass: 'swal-wide',
                    timer: 1500
                });

                $('#view_learning_development_modal').modal('hide');

                var activeTabIndex = localStorage.getItem('activeTabIndex');

    if (activeTabIndex) {
        setTimeout(() => {
            var tabElement = $("#" + activeTabIndex);
            if (tabElement.length && tabElement.is(':visible')) {
                tabElement[0].click();
            } else {
                if ($('#tab2-tab').is(':visible')) {
                    $('#tab2-tab')[0].click();
                    localStorage.setItem('activeTabIndex', 'tab2-tab');
                } else {
                    var firstVisibleTab = $('.nav-tabs a:visible:first').attr('id');
                    if (firstVisibleTab) {
                        $('#' + firstVisibleTab)[0].click();
                        localStorage.setItem('activeTabIndex', firstVisibleTab);
                    }
                }
            }
        }, 200); 
    }

                get_learning_development_evaluation_list();               
                // table.ajax.reload();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    text: 'Please check the inputs and try again.',
                });
            }
        },
        error: function (xhr) {
            Swal.fire({
                icon: 'error',
                title: 'Update Failed',
                text: 'There was an issue updating the rating.',
            });
        }
    });
});




    $('#update_learning_devlopment_idea').submit(function(e) {
        e.preventDefault();
            $.ajax({
            type: "post",
            url: "<?php echo e(url('update_learning_devlopment_idea')); ?>",
            data: { 
                "_token": "<?php echo e(csrf_token()); ?>",
                'learning_development_idea_id': $('#learning_development_idea_id').val(),
            },
            success: function(data) {
                if (data == 1) {
                    Swal.fire({
                        position: 'top-end',
                        icon: 'success',
                        title: 'Updated Successfully',
                        showConfirmButton: false,
                        customClass: 'swal-wide',
                        timer: 1500
                    });
                    $("#learning_devlopment_idea_table").DataTable().ajax.reload();
                }
            }
        });
    });

function historylearningideamodal(id) {
    var _token = $('#csrf_token').val();
    $.ajax({
        type: "POST",
        url: "<?php echo e(url('history_view_learning_development')); ?>",
        data: {
            _token: _token,
            id: id,
        },
        beforeSend: function() {},
        success: function(data) {
            $('#historylearningideatasks').modal('show');
            $('#history_learning_developement_idea').html(data);
        }
    });
}
    /* editlearningideatasks End */
    /* View  learning ideatasks End */
    function viewlearningideamodal(id) {
        var _token = $('#csrf_token').val();
        // alert('test');
        $.ajax({
                type: "POST",
                url: "<?php echo e(url('get_view_learning_development')); ?>",
                data: {
                    _token: _token,
                    id:id,
                },
                beforeSend: function() {},
                success: function(data) {
                    //alert(data);
                    $('#viewlearningideatasks').modal('show');
                    $('#show_learning_developement_idea').html(data);
                    $("#idea_category_edit").change(function() {
  // alert($("#idea_category").val());
  
  if($("#idea_category_edit").val()=="1")
    {
        $(".iiy_form_field_hide").hide();     
        $(".product").show(); 
        $(".common").show();
        $(".brand_cover").show();
    }
    else if($("#idea_category_edit").val()=="2")
    {
        $(".iiy_form_field_hide").hide();     
        $(".squees").show(); 
        $(".common").show();
        $(".brand_cover").show();
    }  else if($("#idea_category_edit").val()=="3")
    {
        $(".iiy_form_field_hide").hide();     
        $(".method").show(); 
        $(".common").show();
    } else if($("#idea_category_edit").val()=="4")
    {
        $(".iiy_form_field_hide").hide();     
        $(".research").show(); 
        $(".common").show();
    }   
});
                    
                    $('.select2').select2();

                }
            }); 
    } 
    /* view learningideatasks End */

    $("#tab1-tab").click(function() {
        $("#tab_value").val('1');
        $("#learning_devlopment_table").DataTable().destroy();
       get_learning_devlopment_list();
    });

    $("#tab0-tab").click(function() {
        $("#tab_value").val('4');
        $("#learning_devlopment_table_value").DataTable().destroy();
        get_learning_development_evaluation_list();
    })

    
    $("#tab2-tab").click(function() {
        $("#tab_value").val('3');
        $("#tab2-tab").attr("value",'2');
        $("#learning_devlopment_idea_table").DataTable().destroy();
        get_learning_devlopment_idea_list();
        get_insight_idea_list();
        //get_ideas_learnt();
    });
    $("#tab4-tab").click(function() {
        $("#learning_devlopment_idea_status").DataTable().destroy();
        $('#viewModal').modal('show');
        get_learning_devlopment_idea_status();
    });
    $("#tab5-tab").click(function() {
        get_insight_ideas_grms();
    });
    $("#idea_status").click(function(){
        window.location.reload();
    });
    $("#tab3-tab").click(function() {
        $("#tab_value").val('2');
        $("#insights_devlopment_table").DataTable().destroy();
        get_learning_insight();
        //get_topics_learnt();
        //get_topics();
     });

 function get_insight_idea_list() {
    $.ajax({
        method: "get",
        url: "<?php echo e(url('fetch_insight_idea_list')); ?>",
        data: { "_token": "<?php echo e(csrf_token()); ?>" },
        dataType: "JSON",
        success: function(response) {
            var select = $("#idea_insight"),
                edit_idea_select = $("#edit_idea_insight");

            select.empty();
            edit_idea_select.empty();

            if (response.length > 0) {
                response.forEach(function(item) {
                    var option = `<option value='${item.id}'>${item.insights_generated}</option>`;
                    select.append(option);
                    edit_idea_select.append(option);
                });
            } else {
                select.append("<option value=''>No insights available</option>");
                edit_idea_select.append("<option value=''>No insights available</option>");
            }
        },
        error: function(xhr) {
        }
    });
}

  

  

    

    // Topic learnt select
 $(document).on('change', '#topic_learnt_create_insight', (e) => {
    e.preventDefault();

    let topic_id = $(e.currentTarget).val();
    let topic_information = topicsLearntData.find(topic => topic.id == topic_id);

  

    if (topic_information) {
        $('#insight_category').val(topic_information.source_learning).trigger('change');

        if (topic_information.area_of_development == 11) {
            if ($('#insight_category_1 option[value="Other"]').length === 0) {
                $('#insight_category_1').append('<option value="Other">Other</option>');
            }

            $('#insight_category_1').val("Other").trigger('change');
            $('#other_insight_div').removeClass('d-none');
            $('#other_insight').val(topic_information.other_field).prop('disabled', false);

            $('#project_insight_div').addClass('d-none');
            $('#project_field').val("").prop('disabled', true);
        } 
        else if (topic_information.area_of_development == 12) {
            if ($('#insight_category_1 option[value="Project"]').length === 0) {
                $('#insight_category_1').append('<option value="Project">Project</option>');
            }

            $('#insight_category_1').val("Project").trigger('change');
            $('#project_insight_div').removeClass('d-none');
            $('#project_insight').val(topic_information.project_name || "").prop('disabled', false);
            
            $('#other_insight_div').addClass('d-none');
            $('#other_insight').val("").prop('disabled', true);
        } 
        else {
            $('#insight_category_1').val(topic_information.area_of_development).trigger('change').prop('disabled', false);
            
            $('#other_insight_div').addClass('d-none');
            $('#other_insight').val("").prop('disabled', true);
            $('#project_insight_div').addClass('d-none');
            $('#project_insight').val("").prop('disabled', true);

            $('#insight_category_1 option[value="Other"]').remove();
            $('#insight_category_1 option[value="Project"]').remove();
        }
    } 
});

    function get_insight_ideas_grms() {
        $("#insights_idea_gms").removeClass('d-none');
        var table11 = $("#insights_idea_gms").DataTable({
            ajax: {
                url: "<?php echo e(url('insights_idea_gms')); ?>",
                type: 'get',
            },
            columns: [
                {data: 'DT_RowIndex' 
                ,name: 'DT_RowIndex' },
                {
                    data: '1000gms',
                    name: '1000gms'
                },
            ]
            
        });
    }



$('#idea_insight').change(function () {
    var tempcsrf = $('#csrf_token').val();
    var idea_insight = $(this).val();
    var authUserId = <?php echo e(Auth::user()->id); ?>;
    
    $.ajax({
        type: "POST",
        url: "<?php echo e(url('get_scientist_insight')); ?>",
        data: {
            idea_insight: idea_insight,
            _token: "<?php echo e(csrf_token()); ?>",
            auth_user_id: authUserId
        },
        success: function (data) {
            var scientistDropdown = $('#scientist');
            scientistDropdown.empty().append('<option value="0">Select</option>');

            var scientistDropdownEdit = $('#edit_idea_scientist');
            scientistDropdownEdit.empty().append('<option value="0">Select</option>');

            if (data.length > 0) {
                data.forEach(function (item) {
                    // Ensure only scientists not equal to logged-in user are shown
                    if (item.scientist_name !== null) {
                        scientistDropdown.append(
                            `<option value="${item.scientist_id}">${item.scientist_name}</option>`
                        );
                        scientistDropdownEdit.append(
                            `<option value="${item.scientist_id}">${item.scientist_name}</option>`
                        );
                    }
                });

                if (scientistDropdown.find('option').length === 2) {
                    scientistDropdown.val(scientistDropdown.find('option:eq(1)').val()).trigger('change');
                }
                if (scientistDropdownEdit.find('option').length === 2) {
                    scientistDropdownEdit.val(scientistDropdownEdit.find('option:eq(1)').val()).trigger('change');
                }
            }
        },
        error: function (xhr, status, error) {
        }
    });
});





    $(document).ready(function() {
        $("#learning_devlopment_idea_table").dataTable().fnDestroy();
        get_learning_devlopment_idea_list();  

        document.getElementById('monthgoal').addEventListener('keydown', function(event) {
            event.preventDefault();
        });

    });

    function get_learning_devlopment_idea_list()
 {
        var tempcsrf = $('#csrf_token').val();
        var table11 = $("#learning_devlopment_idea_table").DataTable({
            "scrollX": true,
            "order": [1, 'asc'],
            "pageLength": 10,
            "fixedHeader": true,
            "processing": true,
            "paging": true,
            "ordering": false,
            "info": true,
            "searching": true,
            "bDestroy": true,
            "oLanguage": {
                "sProcessing": "<div id='overlay'><h2>Loading .. Please wait</h2><i class='fa fa-spinner fa-spin fa-3x fa-fw'></i><span class='sr-only'>Loading...</span></div>",
            },
            dom: 'Bfrtip',
            // buttons: [
            //     'copyHtml5',
            //     'excelHtml5',
            //     'csvHtml5',
            //     'pdfHtml5'
            // ],
            buttons: [
                {
                    extend: 'copy',
                    title: "Idea list",
                    customize: function(data) {
                        var rows = data.split('\n');
                        for (var i = 3; i < rows.length; i++) {
                            var cells = rows[i].split(/\s*,\s*/);
                            var parts = cells[0].split('\t');
                            parts.shift();
                            cells[0] = parts.join('\t');
                            cells.unshift(i-2);
                            rows[i] = cells.join('\t');
                        }
                        data = rows.join('\n');
                        return data;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                },
                {
                    extend: 'csv',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Idea list - ${username}`;
                    },
                    customize: function(csv) {
                        var rows = csv.split('\n');
                        for (var i = 1; i < rows.length; i++) {
                            var cells = rows[i].split(',');
                            cells[0] = i;
                            rows[i] = cells.join(',');
                        }
                       
                        csv = rows.join('\n');
                        return csv;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                },
                // 'excel',
                {
                    extend: 'pdf',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Idea list - ${username}`;
                    },
                    orientation : 'landscape',
                    pageSize : 'A2',
                    customize : function(doc) {
                        doc.defaultStyle.fontSize = 12; //2, 3, 4,etc
                        doc.styles.tableHeader.fontSize = 14; //2, 3, 4, etc
                        doc.content[1].table.widths = Array(doc.content[1].table.body[0].length + 1).join('*').split('');
                        doc.content[1].table.body.forEach(function(row, i) {
                            if (i > 0) {
                                row.splice(0, 1);
                                row.unshift(i);
                            }
                             
                        });
                        // doc.styles['td:nth-child(8)'] = {
                        //     width: '1000px',
                        //     'max-width': '1000px'
                        // }
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                },
                {
                    extend: 'excel',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Idea list - ${username}`;
                    },
                    customize: function(xlsx) {
                        var sheet = xlsx.xl.worksheets['sheet1.xml'];
                        var rows = $('row', sheet);
                        rows.each(function (index) {
                            if (index > 0) {
                                var cells = $(this).find('c');
                                var cell = cells.eq(0);
                                cell.find('v').text(index-1);
                            }
                        });
                        return xlsx;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                }
            ],
            "columnDefs": [{
                "targets": 'no-sort',
                "orderable": true,
                "searchable": true,
            }],
            ajax: {
                url: "<?php echo e(url('get_learning_devlopment_idea_list')); ?>",
                type: 'get',
                data: {
                  
                },
                data: function(d) {
                    // d.from_date = from_date;
                    // d.to_date = to_date;
                    d.month_idea = $('#month_idea').val();
                    d.scientistfilter_cl = $('#scientistfilter_idea_cl').val();
                    d.scientistfilter_tl = $('#scientistfilter_idea').val();
                    // d.statusfilter = $('#statusfilter').val();
                    // d.idea_filter = $('#idea_wtfilter').val();
                    d.scientistfilter_member = $('#scientistfilter_member_idea').val();
                },
                dataSrc: function(json){
                    $('#idea_count').text(json.recordsTotal);
                    var countsHtml = '';
                    json.scientistCounts.forEach(function (item) {
                        countsHtml += `<div>${item.scientist_name}: ${item.count}</div>`;
                    });
                $('#idea_scientist_counts').html(countsHtml);
                    return json.data;
                }
               
            },
            columns: [
        {
        data: null,
        name: 's.no',
        render: function (data, type, row, meta) {
            return meta.row + 1;
        }
      },
    {
        data: 'actions',
        name: 'actions'
    },
    {
        data: 'idea_category',
        name: 'idea_category' 
    },
    {
        data: 'idea_type',
        name: 'idea_type'
    },
    {
        data: 'insights',
        name: 'insights'
    },
    {
        data: 'scientist',
        name: 'scientist' 
    },
    {
        data: 'scientist_name',
        name: 'scientist_name',
    } ,
    {
        data: 'idea_txt',
        name: 'idea_txt' 
    },
    {
        data: 'ideas_comments',
        name: 'ideas_comments' 
    },
    {
        data: 'target_date',
        name: 'target_date', 
    },
   
   
    ]

        });
        if ($('#authid').val() == 74) {
            table11.column('actions:name').visible(false);
        }

        if ($('#roleuser').val() == 2) {
            table11.column('created_by:name').visible(false);
        }else{
            table11.column('created_by:name').visible(true);
        }
        
        table11.column('idea_comments').visible(false);
         if ($('#roleuser').val() == 2) {
            
            table11.column('supervisior_name:name').visible(false);
        }else {
            
            table11.column('supervisior_name:name').visible(true);
        }
          // Trigger reload on filter change
    $('#scientistfilter_member_idea').on('change', function() {
        table11.ajax.reload();
    });
    $('#scientistfilter_idea_cl').on('change', function() {
        table11.ajax.reload();
    });
    $('#scientistfilter_idea').on('change', function() {
        table11.ajax.reload();
    });
}
    $('#statusfilter').change(function() {
        get_learning_devlopment_idea_list()
    });
    $('#idea_wtfilter').change(function() {
        get_learning_devlopment_idea_list()
    });
    var start = 0;
    var length = 0;
    var totalRecords = 0;
    var table11;

    $(document).ready(function() {
    $("#learning_devlopment_idea_status").dataTable().fnDestroy();
        // $('#viewModal').modal('show');
        // get_learning_devlopment_idea_status();   
    });  


        $("#loadMoreBtn").click(function() {
        var records = parseInt($('#loadMoreBtn').val());
        // alert(records)
        if (!isNaN(records) && records !== 0) {
            length += records;
            get_learning_devlopment_idea_status(start, length);
        } else {
            length += 10;
            if(length ==10){
                length +=10;
            }
            get_learning_devlopment_idea_status(start, length);
        }
        
        });
        $(document).ready(function() {
            // $('.close').on('click', function() {
            // var a= $( "#ddd" ).val();
            //     alert(a)
            // });
        $("#showEntriesSelect").on('change', function() {
            var selectedValue = $(this).val();
            loadedRecords(selectedValue);
        });
        });
        function loadedRecords(selectedValue){
        $('#loadMoreBtn').val(selectedValue);
        }

       
       
        $('#l_d_modal').on('click', function() {
            $( "#l_d_number" ).trigger( "reset" );
        });

        
        
        
        function  get_reporting_scientist_scientistfilter_idea()
        {
            $.ajax({
                type: "get",
                url: "<?php echo e(url('get_reporting_scientist')); ?>",
                data: {
                    "_token": "<?php echo e(csrf_token()); ?>",
                    'scientist': $('#scientistfilter_idea').val(),
                },
                success: function(data) {
                    document.getElementById("scientistfilter_idea_div").style.display = "block";

                    var options = '';
                    options += '<option value=" ">All</option>';

                    for (var i = 0; i < data.length; i++) {

                options += '<option value="' + data[i].name+ '" >' + data[i].name + '</option>';
                    }
                    $("#scientistfilter_idea_member").html(options);
                    
                }
            });
        }
    function deleteL_D_idea(id,idea_is_mapped) {
        
        $.confirm({
            title: idea_is_mapped ? ' This idea is mapped with R&D projects. Are you sure you want to delete?' : 'Are you sure you want to delete?',
            content: '' +
            'Are you sure you want to delete?',
            buttons: {
                formSubmit: {
                    text: 'Delete',
                    btnClass: 'btn-blue',
                    action: function () {
                        $.ajax({
                            type: "POST",
                            url: "<?php echo e(url('deleteL_D_idea')); ?>",
                            data: {
                                id:id,
                                _token:"<?php echo e(csrf_token()); ?>"
                            },
                            success: function (data) {
                            if (data.length != 0) {
                                var t = JSON.parse(data);
                                if(t['response'] =='success'){
                                    Swal.fire({
                                        position: 'top-end',
                                        icon: 'success',
                                        title: "Deleted Successfully",
                                        showConfirmButton: false,
                                        customClass: 'swal-wide',
                                        timer: 1500
                                    });
                                    $("#learning_devlopment_idea_table").DataTable().ajax.reload();
                                    setTimeout(function() {
                                    }, 2000);
                                }else{
                                    Swal.fire({
                                        position: 'top-end',
                                        icon: 'error',
                                        title: "Delete Failed",
                                        showConfirmButton: false,
                                        customClass: 'swal-wide',
                                        timer: 1500
                                    });
                                }
                            }
                            }
                        });
                    }
                },
                cancel: function () {
                    //close
                    var newVal = $(e).val();
                    if(newVal==1){
                        $(e).val(0);
                    }else{
                        $(e).val(1);
                    }
                },
            },
        });
    }
    $('#month_idea').change(function() {
        $("#learning_devlopment_idea_table").DataTable().destroy();
        get_learning_devlopment_idea_list();
    });
    $('#scientistfilter_idea').change(function() {
        scientist_for_tl();
        $("#learning_devlopment_idea_table").DataTable().destroy();
        get_learning_devlopment_idea_list();
    });
    $('#scientistfilter_idea_cl').change(function() {
        scient_cl();
        $("#learning_devlopment_idea_table").DataTable().destroy();
        get_learning_devlopment_idea_list()
    });
    $('#scientistfilter_member_idea').change(function() {
        $("#learning_devlopment_idea_table").DataTable().destroy();
        get_learning_devlopment_idea_list()
    });

    function get_learning_insight() {
        var tempcsrf = $('#csrf_token').val();
        var table12 = $("#insights_devlopment_table").DataTable({
            "scrollX": true,
            "order": [1, 'asc'],
            "fixedHeader": true,
            "pageLength": 10,
            "processing": true,
            "serverSide": true,
            "paging": false,
            "ordering": false,
            "info": true,
            "searching": true,
            "oLanguage": {
                "sProcessing": "<div id='overlay'><h2>Loading .. Please wait</h2><i class='fa fa-spinner fa-spin fa-3x fa-fw'></i><span class='sr-only'>Loading...</span></div>",
            },
            dom: 'Bfrtip',
            buttons: [
                {
                extend: 'copy',
                    title: "Idea list",
                    customize: function(data) {
                        var rows = data.split('\n');
                        for (var i = 3; i < rows.length; i++) {
                            var cells = rows[i].split(/\s*,\s*/);
                            var parts = cells[0].split('\t');
                            parts.shift();
                            cells[0] = parts.join('\t');
                            cells.unshift(i-2);
                            rows[i] = cells.join('\t');
                        }
                        data = rows.join('\n');
                        return data;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                },
                {
                    extend: 'csv',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Idea list - ${username}`;
                    },
                    customize: function(csv) {
                        var rows = csv.split('\n');
                        for (var i = 1; i < rows.length; i++) {
                            var cells = rows[i].split(',');
                            cells[0] = i;
                            rows[i] = cells.join(',');
                        }
                       
                        csv = rows.join('\n');
                        return csv;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                },
                // 'excel',
                {
                    extend: 'pdf',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Idea list - ${username}`;
                    },
                    orientation : 'landscape',
                    pageSize : 'A2',
                    customize : function(doc) {
                        doc.defaultStyle.fontSize = 12; //2, 3, 4,etc
                        doc.styles.tableHeader.fontSize = 14; //2, 3, 4, etc
                        doc.content[1].table.widths = Array(doc.content[1].table.body[0].length + 1).join('*').split('');
                        doc.content[1].table.body.forEach(function(row, i) {
                            if (i > 0) {
                                row.splice(0, 1);
                                row.unshift(i);
                            }
                             
                        });
                        // doc.styles['td:nth-child(8)'] = {
                        //     width: '1000px',
                        //     'max-width': '1000px'
                        // }
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                },
                {
                    extend: 'excel',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Idea list - ${username}`;
                    },
                    customize: function(xlsx) {
                        var sheet = xlsx.xl.worksheets['sheet1.xml'];
                        var rows = $('row', sheet);
                        rows.each(function (index) {
                            if (index > 0) {
                                var cells = $(this).find('c');
                                var cell = cells.eq(0);
                                cell.find('v').text(index-1);
                            }
                        });
                        return xlsx;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                }                ],
            "columnDefs": [{
                "targets": 'no-sort',
                "orderable": true,
                "searchable": true,
            }],
            ajax: {
                url: "<?php echo e(url('get_learning_devlopment_insight')); ?>",
                type: 'get',
                data: {
                    "_token": "<?php echo e(csrf_token()); ?>",
                    "month_insight": $('#month_insight').val(),
                },
                data: function(d) {
                    
                    d.month_insight = $('#month_insight').val();
                    d.scientistfilter_member = $('#scientistfilter_member_insight').val();
                    d.scientistfilter_tl = $('#scientistfilter_insight_cl').val();
                    d.scientistfilter_cl = $('#scientistfilter_insight').val();
                },
                dataSrc: function(json) {
                    // Update the count dynamically
                    $('#insight_count').text(json.recordsTotal);

                    var countsHtml = '';
                json.scientistCounts.forEach(function (item) {
                    countsHtml += `<div>${item.scientist_name}: ${item.count}</div>`;
                });
                $('#scientist_counts').html(countsHtml);

                return json.data;
                },
                error: function(xhr, error, thrown) {
                    alert(error);
                }
            },
            columns: [
                {
                    data: null,
                    name: 's.no',
                    render: (data, type, row, meta) => meta.row + 1
                },
                { data: 'action', name: 'action' },
                { data: 'insights_generated', name: 'insights_generated' },
                { data: 'insights_category', name: 'insights_category' },
                { data: 'scientist', name: 'scientist' },
                { data: 'remarks', name: 'remarks' }
            ]
        });

        $('#scientistfilter_member_insight').on('change', function() {
        table12.ajax.reload(); 
        });

        // Reload table data when filters change
        $('#scientistfilter_insight').change(function() {
            table12.ajax.reload();
        });
        $('#scientistfilter_insight_cl').on('change', function() {
            table12.ajax.reload(); 
        });
        $('#scientistfilter_insight').on('change', function() {
            table12.ajax.reload(); 
        });
            
    } 

    function deleteL_D_insight(id) {
        $.confirm({
            title: 'Delete',
            content: '' +
            'Are you sure you want to delete?',
            buttons: {
                formSubmit: {
                    text: 'Delete',
                    btnClass: 'btn-blue',
                    action: function () {
                        $.ajax({
                            type: "POST",
                            url: "<?php echo e(url('deleteL_D_insight')); ?>",
                            data: {
                                id:id,
                                _token:"<?php echo e(csrf_token()); ?>"
                            },
                            success: function (data) {
                            if (data.length != 0) {
                                var t = JSON.parse(data);
                                if(t['response'] =='success'){
                                    Swal.fire({
                                        position: 'top-end',
                                        icon: 'success',
                                        title: "Deleted Successfully",
                                        showConfirmButton: false,
                                        customClass: 'swal-wide',
                                        timer: 1500
                                    });
                                    $("#insights_devlopment_table").DataTable().ajax.reload();
                                    setTimeout(function() {
                                    }, 2000);
                                }else{
                                    Swal.fire({
                                        position: 'top-end',
                                        icon: 'error',
                                        title: "Delete Failed",
                                        showConfirmButton: false,
                                        customClass: 'swal-wide',
                                        timer: 1500
                                    });
                                }
                            }
                            }
                        });
                    }
                },
                cancel: function () {
                    //close
                    var newVal = $(e).val();
                    if(newVal==1){
                        $(e).val(0);
                    }else{
                        $(e).val(1);
                    }
                },
            },
        });
    }

    $('#month_insight').change(function() {
        $("#insights_devlopment_table").DataTable().destroy();
        get_learning_insight();

    });
    // $('#scientistfilter_insight').change(function() {
    //     scientist_for_tl();
    //     $("#insights_devlopment_table").DataTable().destroy();
    //     get_learning_insight();
    // });
    
    // $('#scientistfilter_insight_cl').change(function() {
    //     scient_cl();
    //     $("#insights_devlopment_table").DataTable().destroy();
    //     get_learning_insight()
    // });

    // $('#scientistfilter_member_insight').change(function() {
    //     $("#insights_devlopment_table").DataTable().destroy();
    //     get_learning_insight()
    // });
    $(document).ready(function() {
        $("#insights_devlopment_table").dataTable().fnDestroy();
        get_learning_insight(); 
    });

    $(document).ready(function() {        

        document.getElementById('monthvalue').addEventListener('keydown', function(event) {
            event.preventDefault();
        });

    });


   

   

    $('#date_range').daterangepicker({
    autoUpdateInput: false,
    locale: {
        format: 'YYYY-MM-DD',
        separator: ' - ',
        cancelLabel: 'Clear'
    }
});

$('#date_range').on('apply.daterangepicker', function(ev, picker) {
    $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
});

$('#date_range').on('cancel.daterangepicker', function(ev, picker) {
    $(this).val('');
});



    $(document).ready(function () {
            $('#edit_date_range').daterangepicker({
    locale: {
        format: 'YYYY-MM-DD',
        separator: ' - '
    }
});

    });

//     $('#scientistfilter_evaluation, #scientistfilter_member_idea').on('change', function () {
//     var selectedMember = $(this).val();
//     console.log('Selected Scientist:', selectedMember);

//     $(this).select2?.('close');

//     setTimeout(function() {
//         $('#learning_devlopment_table_value').DataTable().ajax.reload();
//     }, 100);
// });




    function get_learning_development_evaluation_list() {
        var tempcsrf = $('#csrf_token').val();

        if ( $.fn.DataTable.isDataTable('#learning_devlopment_table_value') ) {
            $('#learning_devlopment_table_value').DataTable().clear().destroy();
        }

        var table1 = $("#learning_devlopment_table_value").DataTable({
             'rowsGroup': [1,2],
            //"scrollX": true,
            "order": [1, 'asc'],
            "fixedHeader": true,
             "pageLength": 10,
            "processing":true,
            "serverSide": true,
            "paging": false,
            "ordering": false,
            "info": true,
            "searching": true,
            "oLanguage": {
                "sProcessing": "<div id='overlay'><h2>Loading .. Please wait</h2><i class='fa fa-spinner fa-spin fa-3x fa-fw'></i><span class='sr-only'>Loading...</span></div>",
            },
            dom: 'Bfrtip',
            buttons: [
                'copyHtml5',
                'excelHtml5',
                'csvHtml5',
                'pdfHtml5'
            ],
            buttons: [
                {
                    extend: 'copy',
                    title: "Devlopment list",
                    customize: function(data) {
                        var rows = data.split('\n');
                        for (var i = 3; i < rows.length; i++) {
                            var cells = rows[i].split(/\s*,\s*/);
                            var parts = cells[0].split('\t');
                            parts.shift();
                            cells[0] = parts.join('\t');
                            cells.unshift(i-2);
                            rows[i] = cells.join('\t');
                        }
                        data = rows.join('\n');
                        return data;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                },
                {
                    extend: 'csv',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Evaluation list - ${username}`;
                    },
                    // title: "Devlopment list",
                    
                    customize: function(csv) {
                        var rows = csv.split('\n');
                        for (var i = 1; i < rows.length; i++) {
                            var cells = rows[i].split(',');
                            cells[0] = i;
                            rows[i] = cells.join(',');
                        }
                       
                        csv = rows.join('\n');
                        return csv;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                },
                // 'excel',
                {
                    extend: 'pdf',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Evaluation list - ${username}`;
                    },
                    // title: "Devlopment list",
                    orientation : 'landscape',
                    pageSize : 'A2',
                    customize : function(doc) {
                        doc.defaultStyle.fontSize = 12; //2, 3, 4,etc
                        doc.styles.tableHeader.fontSize = 14; //2, 3, 4, etc
                         doc.content[1].table.widths = Array(doc.content[1].table.body[0].length + 1).join('*').split('');
                        doc.content[1].table.body.forEach(function(row, i) {
                            if (i > 0) {
                                row.splice(0, 1);
                                row.unshift(i);
                            }
                             
                        });
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                },
                {
                    extend: 'excel',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Evaluation list - ${username}`;
                    },
                    // title: "Devlopment list",
                    customize: function(xlsx) {
                        var sheet = xlsx.xl.worksheets['sheet1.xml'];
                        var rows = $('row', sheet);
                        rows.each(function (index) {
                            if (index > 0) {
                                var cells = $(this).find('c');
                                var cell = cells.eq(0);
                                cell.find('v').text(index-1);
                            }
                        });
                        return xlsx;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                }
            ],
            "columnDefs": [{
                // "targets": 'no-sort',
                "orderable": true,
                "searchable": true,
            }],
            ajax: {
                url: "<?php echo e(url('get_learning_development_evaluation_list')); ?>",
                type: 'GET',
                data: {
                    "_token": "<?php echo e(csrf_token()); ?>",
                    "monthgoal": $('#monthgoal').val(),
                    "scientistfilter_evaluation": $('#scientistfilter_evaluation').val(),
                },
                data: function(d) {
                    console.log('#scientistfilter_evaluation', $('#scientistfilter_evaluation').val());
                    
                        d.monthgoal = $('#monthgoal').val();
                         d.scientistfilter_cl = $('#scientistfilter_evaluation_cl').val();
                        // d.scientistfilter_tl = $('#scientistfilter_evaluation').val();
                         d.scientistfilter_member = $('#scientistfilter_evaluation').val();
                },
                 error: function(xhr, error, thrown) {
                    alert(error);
                }
            },
            columns: [
                {
                    data: null,
                    name: 's.no',
                    render: (data, type, row, meta) => meta.row + 1
                },
                { data: 'actions', name: 'actions' },
                { data:   'topic_learnt', name: 'topic_learnt'},
                { data: 'topic_presented', name: 'topic_presented'},
                { data:  'team_member', name: 'team_member'},
                {data:    'rating', name: 'rating'},
                {data:  'feedback', name: 'feedback'},
                {data:  'weekly_presentation', name: 'weekly_presentation'},


                            
              
                
            ]
        });
    //     $('#scientistfilter_evaluation').on('change', function() {
    //     table1.ajax.reload();
    // });
    }
    $('#monthvalue').change(function() {
        $("#learning_devlopment_table_value").DataTable().destroy();
        get_learning_development_evaluation_list();
    });
    $('#scientistfilter_evaluation').change(function() {
        scientist_for_tl();
        $("#learning_devlopment_table_value").DataTable().destroy();
        get_learning_development_evaluation_list();
    });
    $('#scientistfilter_evaluation_cl').change(function() {
        scient_cl();
        $("#learning_devlopment_table_value").DataTable().destroy();
        get_learning_development_evaluation_list()
    });
    $('#scientistfilter_member_idea').change(function() {
        $("#learning_devlopment_table_value").DataTable().destroy();
        get_learning_development_evaluation_list()
    });


    function get_learning_devlopment_list() {
        var tempcsrf = $('#csrf_token').val();

        var table1 = $("#learning_devlopment_table").DataTable({
            'rowsGroup': [1,2],
            "scrollX": true,
            // "order": [1, 'asc'],
            "fixedHeader": true,
            "pageLength": 10,
            "processing":true,
             "serverSide": true,
            "paging": false,
            "ordering": false,
            "info": true,
            "searching": true,
            "oLanguage": {
                "sProcessing": "<div id='overlay'><h2>Loading .. Please wait</h2><i class='fa fa-spinner fa-spin fa-3x fa-fw'></i><span class='sr-only'>Loading...</span></div>",
            },
            dom: 'Bfrtip',
            // buttons: [
            //     'copyHtml5',
            //     'excelHtml5',
            //     'csvHtml5',
            //     'pdfHtml5'
            // ],
            buttons: [
                {
                    extend: 'copy',
                    title: "Devlopment list",
                    customize: function(data) {
                        var rows = data.split('\n');
                        for (var i = 3; i < rows.length; i++) {
                            var cells = rows[i].split(/\s*,\s*/);
                            var parts = cells[0].split('\t');
                            parts.shift();
                            cells[0] = parts.join('\t');
                            cells.unshift(i-2);
                            rows[i] = cells.join('\t');
                        }
                        data = rows.join('\n');
                        return data;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                },
                               {
                    extend: 'csv',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Development_List_${username}`;
                    },
                    customize: function (csv) {
                        var rows = csv.split('\n');
                        for (var i = 1; i < rows.length; i++) {
                            var cells = rows[i].split(',');
                            cells[0] = i;
                            rows[i] = cells.join(',');
                        }
                
                        csv = rows.join('\n');
                        return csv;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                },
                // 'excel',
                               {
                    extend: 'pdf',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Development_List_${username}`;
                    },
                    orientation: 'landscape',
                    pageSize: 'A2',
                    customize: function (doc) {
                        doc.defaultStyle.fontSize = 12; // Adjust font size
                        doc.styles.tableHeader.fontSize = 14; // Adjust table header font size
                        doc.content[1].table.widths = Array(doc.content[1].table.body[0].length + 1).join('*').split('');
                        doc.content[1].table.body.forEach(function (row, i) {
                            if (i > 0) {
                                row.splice(0, 1);
                                row.unshift(i); // Add row numbers
                            }
                        });
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)' // Exclude columns with the class "no-export"
                    }
                },
                                {
                    extend: 'excel',
                    title: function () {
                        let username = "<?php echo e(Auth::user()->name); ?>"; 
                        return `Development_List_${username}`;
                    },
                    customize: function (xlsx) {
                        var sheet = xlsx.xl.worksheets['sheet1.xml'];
                        var rows = $('row', sheet);
                        rows.each(function (index) {
                            if (index > 0) {
                                var cells = $(this).find('c');
                                var cell = cells.eq(0);
                                cell.find('v').text(index-1);
                            }
                        });
                        return xlsx;
                    },
                    exportOptions: {
                        modifier: {
                            selected: null
                        },
                        columns: ':not(.no-export)'
                    }
                }
            ],
            "columnDefs": [{
                "targets": 'no-sort',
                "orderable": true,
                "searchable": true,
            }],
            ajax: {
                url: "<?php echo e(url('get_learning_devlopment_list')); ?>",
                type: 'GET',
                data: {
                    "_token": "<?php echo e(csrf_token()); ?>",
                },
                data: function(d) {
                        d.monthgoal = $('#monthgoal').val();
                        d.scientistfilter_cl = $('#scientistfilter_cl').val();
                        d.scientistfilter_tl = $('#scientistfilter').val();
                        d.scientistfilter_member = $('#scientistfilter_member').val();
                        //console.log(d);
                        
                },
                 error: function(xhr, error, thrown) {
                    alert(error);
                }
            },
            columns: [
                {
                    data: null,
                    name: 's.no',
                    render: (data, type, row, meta) => meta.row + 1
                },
                { data: 'actions', name: 'actions' },
                { data: 'topic_learnt', name: 'topic_learnt'},
                { data:  'topic_presented', name: 'topic_presented'},
                { data: 'scientist', name: 'scientist'},
                {data:    'hours_invested', name: 'hours_invested'},
                {data : 'vendor_facility_visited', name: 'vendor_facility_visited'},
                {data: 'conferences_attended', name: 'conferences_attended'},
                {data: 'exhibitions_attended', name: 'exhibitions_attended'},
                {data: 'consumer_retail_visits', name: 'consumer_retail_visits'},
                {data: 'ideas_generated', name: 'ideas_generated'},
                {data:'date_range', name:'date_range'}
                //{data: 'weekly_presentation', name: 'weekly_presentation'},
                
            ]
            
        });
        $('#scientistfilter_cl').on('change', function() {
            table1.ajax.reload(); 
        });
        $('#scientistfilter').on('change', function() {
            table1.ajax.reload(); 
        });

        if ($('#authid').val() == 74) {
            table1.column('actions:name').visible(false);
        }
        if ($('#roleuser').val() == 3 ) {
            table1.column('supervisior_name:name').visible(false);
        }else{
            table1.column('supervisior_name:name').visible(true);
        }

       
       
    }

    $('#monthgoal').change(function() {
        $("#learning_devlopment_table").DataTable().destroy();
        get_learning_devlopment_list();
    });
    // $('#scientistfilter').change(function() {
    //     scientist_for_tl();
    //     $("#learning_devlopment_table").DataTable().destroy();
    //     get_learning_devlopment_list();
    // });
    // $('#scientistfilter_cl').change(function() {
    //     scient_cl();
    //     $("#learning_devlopment_table").DataTable().destroy();
    //     get_learning_devlopment_list()
    // });
    $('#scientistfilter_member').change(function() {
        // scientist_for_tm();
        $("#learning_devlopment_table").DataTable().destroy();
        get_learning_devlopment_list()
    });

    function delete_lnd(id) {
        $.confirm({
            title: 'Delete',
            content: '' +
            'Are you sure you want to delete?',
            buttons: {
                formSubmit: {
                    text: 'Delete',
                    btnClass: 'btn-blue',
                    action: function () {
                        $.ajax({
                            type: "POST",
                            url: "<?php echo e(url('delete_lnd')); ?>",
                            data: {
                                id:id,
                                _token:"<?php echo e(csrf_token()); ?>"
                            },
                            success: function (data) {
                            if (data.length != 0) {
                                var t = JSON.parse(data);
                                if(t['response'] =='success'){
                                    Swal.fire({
                                        position: 'top-end',
                                        icon: 'success',
                                        title: "Deleted Successfully",
                                        showConfirmButton: false,
                                        customClass: 'swal-wide',
                                        timer: 1500
                                    });
                                    $("#learning_devlopment_table").DataTable().ajax.reload();
                                    setTimeout(function() {
                                    }, 2000);
                                }else{
                                    Swal.fire({
                                        position: 'top-end',
                                        icon: 'error',
                                        title: "Delete Failed",
                                        showConfirmButton: false,
                                        customClass: 'swal-wide',
                                        timer: 1500
                                    });
                                }
                            }
                            }
                        });
                    }
                },
                cancel: function () {
                    //close
                    var newVal = $(e).val();
                    if(newVal==1){
                        $(e).val(0);
                    }else{
                        $(e).val(1);
                    }
                },
            },
        });
    }

    $('#save_l_d').click(function(e) {
        if(($('#l_d_number').val()=='')){
            $.alert({
                title: 'Invalid',
                content: 'Please enter the number',
            });  
        }
        else{
            var _token = $("#csrf_token").val();
            if($('#save_l_d').text()=='Update'){
                url= "<?php echo e(url('update_learning_development')); ?>";
                actions='Update';
            }
           
                            $.ajax({
                                    type: 'POST',
                                    url: "<?php echo e(url('update_learning_development')); ?>",
                                    //dataType: 'json',
                                    data: { 
                                            id:$("#l_d_id").val(),
                                            parameter:$("#l_d_parameter").val(),
                                            number:$("#l_d_number").val(),
                                            _token:_token
                                    },
                                    beforeSend: function () {
                                        // $(document).find('span.error-text').text('');
                                    },
                                    success: function (data) {
                                        var t = JSON.parse(data);
                                        if(t['response'] =='success'){
                                            Swal.fire({
                                                position: 'top-end',
                                                title: 'Success !!!',
                                                //text: "success",
                                                icon: 'success',
                                                timer: 1500
                                            });
                                            $('#learning_devlopment_table').DataTable().ajax.reload(null, false);
                                            $( "#l_d_modal" ).trigger( "click" );
                                        }else{
                                            Swal.fire({
                                                position: 'top-end',
                                                title: 'Failed !!!',
                                                //text: "Failed",
                                                icon: 'error',
                                                timer: 1500
                                            });
                                            //printErrorMsg(t.error);
                                        }
                                    }
                            });
                    
        }
    });



let ratingInput = document.getElementById("view_rating");

function updateRating(value) {
    value = Math.min(5, Math.max(0, value)); 
    ratingInput.value = value + "/5";
}

ratingInput.addEventListener("keydown", function (event) {
    let currentValue = parseInt(this.value) || 0;

    if (!/^\d$/.test(event.key) && 
        !["Backspace", "ArrowUp", "ArrowDown", "PageUp", "PageDown", "Tab"].includes(event.key)) {
        event.preventDefault();
    }

    if (event.key === "ArrowUp" || event.key === "PageUp") {
        event.preventDefault();
        if (currentValue < 5) {
            updateRating(currentValue + 1);
        }
    }
    else if (event.key === "ArrowDown" || event.key === "PageDown") {
        event.preventDefault();
        if (currentValue > 0) {
            updateRating(currentValue - 1);
        }
    }
});

ratingInput.addEventListener("input", function () {
    let match = this.value.match(/^(\d)\/5$/);
    if (!match) {
        updateRating(0); 
    }
});

ratingInput.value = "0/5";


$(document).on("click", ".view-iiy-btn", function () {
    let iiyId = $(this).data("id");
    let rating = $(this).data("view_rating");
    $.ajax({
        url: "<?php echo e(url('get_learning_development_details')); ?>",
        type: "GET",
        dataType: "json",
        data: { id: iiyId },
        success: function (data) {

            let fileList = $("#uploaded_files_list");
            fileList.empty();
            let topicsContainer = $("#topics_ratings_container");
            topicsContainer.empty();
            if (data.view_rating) {
             $("#view_rating").val(data.view_rating);
         } else {
             $("#view_rating").val("0/5"); 
         }
         if(data.view_feedback){
             $("#view_feedback").val(data.view_feedback);
         }
         else{
             $("#view_feedback").val("");
         }
            if (data) {
                $("#view_title").val(data.view_title || ""); 
                $("#view_topics_present").val(data.view_topics_present || "");
                $("#title_id_val").val(data.title_id || "");
                $("#view_feedback").val(data.view_feedback || "");
            }

            let ratingText = data.view_rating || "0/5";
            let ratingsArray = ratingText.split("/").map(num => parseInt(num.trim(), 10));

            let topicsArray = data.view_title ? data.view_title.split(",") : [];

            topicsArray.forEach((topic, index) => {
                let ratingValue = ratingsArray[index] !== undefined ? ratingsArray[index] : 0;

                topicsContainer.append(`
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Topics Learnt <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" value="${topic.trim()}" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Rating <span class="text-danger">*</span></label>
                                <input type="number" class="form-control rating-input" 
                                    value="${ratingValue}" min="0" max="5" step="1" data-index="${index}">
                            </div>
                        </div>
                    </div>
                `);
            });

            if (Array.isArray(data.uploaded_files_list) && data.uploaded_files_list.length > 0) {
                data.uploaded_files_list.forEach(filePath => {
                    let fileName = filePath.split('/').pop();
                    let fileExtension = fileName.split('.').pop().toLowerCase();
                    let fileURL = filePath;

                   // console.log('fileURL' , fileURL);
                    let fileIcon = "fa-file-alt";
                    if (fileExtension === "pdf") {
                        fileIcon = "fa-file-pdf text-danger";
                    } else if (["doc", "docx"].includes(fileExtension)) {
                        fileIcon = "fa-file-word text-primary";
                    } else if (["ppt", "pptx"].includes(fileExtension)) {
                        fileIcon = "fa-file-powerpoint text-warning";
                    }

                  let maxLength = 25; 

                    fileList.append(`
                        <li class="list-group-item d-flex align-items-center file-item" 
                            style="margin-bottom: 8px; margin-top: 10px; background-color: #d5f2ff; border-radius: 3px;">
                            <i class="fa ${fileIcon} me-2" style="font-size:18px"></i>  
                            <a href="${fileURL}" target="_blank" class="file-link" 
                            data-bs-toggle="tooltip" data-bs-placement="top" 
                            title="${fileName}">
                            ${fileName.length > maxLength ? fileName.substring(0, maxLength) + '...' : fileName}
                            </a>                        
                        </li>
                    `);


                });

                $('[data-bs-toggle="tooltip"]').tooltip();
            } else {
                fileList.append("<li class='list-group-item text-danger'>No file uploaded.</li>");
            }

            $("#view_learning_development_modal").modal("show");
        },
        error: function (xhr) {
            alert("Failed to load details. Please try again.");
        }
    });
});









   
    $(document).ready(function(){
        var activeTab = localStorage.getItem('activeTab');
        if(activeTab){
            $('.nav-tabs a[href="#' + activeTab + '"]').tab('show');
        } else {
            $('.nav-tabs a[href="#tab1"]').tab('show');
        }
        $('.nav-tabs a').on('click', function(){
            var tabId = $(this).attr('href').substring(1); 
            localStorage.setItem('activeTab', tabId); 
        });

        $('.nav-tabs a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
            localStorage.setItem('activeTab', $(e.target).attr('href').substr(1));
        });
    });



    $('#idea_cate').change(function(e){
        
    if($(this).val()=='parent_idea'){
       $('.parent').removeClass('d-none');
       $('.child').addClass('d-none');
       $('.parent_des').addClass('d-none');
       $('.child_idea').addClass('d-none');

    }else if($(this).val()=='child_idea'){
       $('.child').removeClass('d-none');
       $('.parent').addClass('d-none');
       $('.parent_des').removeClass('d-none');
       $('.child_idea').removeClass('d-none');
    }else{
        $('.parent').addClass('d-none');
        $('.child').addClass('d-none');
        $('.parent_des').addClass('d-none');
        $('.child_idea').removeClass('d-none');
    }

        $('#mention_milestone_div').addClass('d-none');
        $('#reason_not_initiated_div').addClass('d-none');
        $('#significant_impact_div').addClass('d-none');
});



function editlearningideamodal(id) {
    var _token = $('#csrf_token').val();
    $.ajax({
    type: "POST",
    url: "<?php echo e(url('get_learning_development')); ?>",
    data: {
    _token: _token,
    id:id,
    },
    beforeSend: function() {},
    success: function(data) {
    $('#editlearningideatasks').modal('show');
    $('#view_learning_developement_idea').html(data);
    $("#idea_category_edit").change(function() {

    if($(this).val()=="1")
    {  
        $(".iiy_form_field_hide").hide();     
        $(".product").show(); 
        $(".common").show();
        $(".brand_cover").show();
        
    }
    else if($(this).val()=="2")
    {
        $(".iiy_form_field_hide").hide();     
        $(".squees").show(); 
        $(".common").show();
        $(".brand_cover").show();
            

    }  else if($(this).val()=="3")
    {
        $(".iiy_form_field_hide").hide();     
        $(".method").show(); 
        $(".common").show();
    } 
    else if($(this).val()=="4")
    {
        $(".iiy_form_field_hide").hide();     
        $(".research").show(); 
        $(".common").show();
    }
});

/* Idea status using Edit change for new and exiting Products Start */
$("#ideastatusproduct_edit").change(function() {
   if($(this).val()==1||$(this).val()==2)
   {
    $(".brandsecond").show();        
    $(".product_category_second").show();      
   }else{
    $(".brandsecond").hide();        
    $(".product_category_second").hide();   
   }
   
   
});
$("#squees_ideastatus_squees_edit").change(function() {
   if($(this).val()==3||$(this).val()==4)
   {
    $(".brandsecond").show();        
    $(".product_category_second").show();      
   }else{
    $(".brandsecond").hide();        
    $(".product_category_second").hide();   
   }
   
   
});
$("#method_ideastatus_squees_edit").change(function() { 
   if($(this).val()==6)
   {
    $(".improvementaccuracy").show(); 
    $(".productsclaims_date").hide(); 
    $(".specificationsdate").hide();    
   }else if($(this).val()==5)
   {
    $(".productsclaims_date").show(); 
    $(".specificationsdate").show();  
    $(".improvementaccuracy").hide();   
   }else{
    $(".improvementaccuracy").hide();   
    $(".productsclaims_date").hide(); 
    $(".specificationsdate").hide();     
   }
  
});
$("#research_ideastatus_research_edit").change(function() {
   if($(this).val()==7)
   {
        $(".brandsecond").show();        
        $(".product_category_second").show();
        $(".time_to_convert").show();      
        if($("#time_to_convert_edit").val()==0 || $("#time_to_convert_edit").val()=='')
        {
            var dateFromPHP = $("#time_to_convert_created_date").val(); 
            var created_date = new Date(dateFromPHP);    
            var today = new Date();
            var difference= Math.abs(today-created_date);
            var days = difference/(1000 * 3600 * 24);
            var days_value=Math.round(Math.abs(days));        
            $("#time_to_convert_edit").val(days_value);
        }
   }else{
    $(".brandsecond").hide();        
    $(".product_category_second").hide();   
    $(".time_to_convert").hide(); 
   }
});
$('.select2').select2();



    }});
} 
    
    function editinsight(id) {
        $('#editinsight' + id).modal('show');
    }

    function updateinsight(id) {        
        var scientistid;
            if ($('#roleuser').val() != 2) {
                scientistid = $('#scientist' + id).val();
            } else {
                scientistid = $('#userid').val();
            }

        $.ajax({
            url: "<?php echo e(url('updateinsight')); ?>",
            data: {
                "_token": "<?php echo e(csrf_token()); ?>",
                'insightid': id,
                'insight_remarks': $('#insight_remarks' + id).val(),
                'insight_category': $('#insight_category' + id).val(),
                'scientist' : scientistid,
                'insight_generated': $('#insight_generated' + id).val()
            },
            type: 'post',
            success: function(response) {
                $('#editinsight' + id).modal('hide');

                if (response == 1) {
                    $.alert({
                        title: 'Success',
                        content: 'Changed Successfully',
                        autoClose: 'logoutUser|300',
                    });
                    $("#insights_devlopment_table").DataTable().ajax.reload();
                }
            }
        });
    }

    function updatelearningideaupdate(id) {
        $.ajax({
            url: "<?php echo e(url('updatelearningidea')); ?>",
            data: {
                "_token": "<?php echo e(csrf_token()); ?>",
                'task_id': id,
                'update_ideacommnets': $('#update_ideacommnets' + id).val()
            },
            type: 'post',
            success: function(response) {
                $('#editlearningideatasks' + id).modal('hide');
                if (response == 1) {
                    $.alert({
                        title: 'Success',
                        content: 'Status Changed successfully',
                        autoClose: 'logoutUser|300',
                    });
                    $("#learning_devlopment_idea_table").DataTable().ajax.reload();
                }
            }
        });
    }

/* Idea status using change for new and exiting Products Start */
$("#ideastatusproduct").change(function() {
   if($(this).val()==1||$(this).val()==2)
   {
    $(".brandsecond").show();        
    $(".product_category_second").show();      
   }else{
    $(".brandsecond").hide();        
    $(".product_category_second").hide();   
   }
   
});
$("#squees_ideastatus_squees").change(function() {
   if($(this).val()==3||$(this).val()==4)
   {
    $(".brandsecond").show();        
    $(".product_category_second").show();      
   }else{
    $(".brandsecond").hide();        
    $(".product_category_second").hide();   
   }
   
   
});
$("#method_ideastatus_squees").change(function() {

   if($(this).val()==6)
   {
    $(".improvementaccuracy").show(); 
    $(".productsclaims_date").hide(); 
    $(".specificationsdate").hide();    
   }else if($(this).val()==5)
   {
    $(".productsclaims_date").show(); 
    $(".specificationsdate").show();  
    $(".improvementaccuracy").hide();   
   }else{
    $(".improvementaccuracy").hide();   
    $(".productsclaims_date").hide(); 
    $(".specificationsdate").hide();     
   }

   
   
});
$("#research_ideastatus_research").change(function() {
   if($(this).val()==7)
   {
    $(".brandsecond").show();        
    $(".product_category_second").show();
    $(".time_to_convert").show();      
   }else{
    $(".brandsecond").hide();        
    $(".product_category_second").hide();   
    $(".time_to_convert").hide(); 
   }  
   
});
function selected_opt(id,selectElement) {
var selectedValue = $(selectElement).val();
Swal.fire({
    icon: 'warning',
    title: "Are you sure you want to update the status?",
    showCancelButton: true,
    confirmButtonColor: '#3085d6',
    cancelButtonColor: '#d33',
    confirmButtonText: 'Yes, Update 😊!'
}).then((result) => {
    if (result.isConfirmed) {
        var feedback = result.value;
        $.ajax({
            url: "<?php echo e(url('updateChildIdeaStatus')); ?>",
            type: 'post',
            data: {
                "_token": "<?php echo e(csrf_token()); ?>",
                'id': id,
                'update_status': selectedValue,
            },
            success: function (response) {
                if (response == 1) {
                    Swal.fire({
                            position: 'top-end',
                            icon: 'success',
                            title: '"Idea Status" Status Changed successfully',
                            showConfirmButton: false,
                            customClass: 'swal-wide',
                            timer: 1500
                        });
                    $("#learning_devlopment_idea_status").DataTable().ajax.reload();
                }
            }
        });
    }
});
}

    //date_picker
    $(function() {
        $('input[name="datefilter"]').daterangepicker({
            autoUpdateInput: false,
            autoApply: true,
            locale: {
                cancelLabel: 'Clear'
            }
        });


        $('input[name="datefilter"]').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY'));
            fromDate = picker.startDate.format('YYYY-MM-DD');
            toDate =  picker.endDate.utc().format('YYYY-MM-DD'); 
        });

        $('input[name="datefilter"]').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });
    });

    
   

    $(document).ready(function() {
    function get_scient_ready() {
        $.ajax({
            type: "get",
            url: "<?php echo e(url('get_reporting_scientist_cl')); ?>",
            dataType: "json",
            success: function(data) {
                console.log("Team Member Filter Debug - Full Data:", data);
                console.log("User Role:", data.user_role);
                console.log("User ID:", data.user_id);
                console.log("All Scientists:", data.scientists);

                var options_cl = '';
                options_cl += '<option value="all">---All---</option>';

                // Handle different user roles for CL/TL selection
                if (data.user_role == 5) {
                    // Role 5 users can see other Role 5 users
                    for (var i = 0; i < data.scientists.length; i++) {
                        if (data.scientists[i].role == 5 && data.scientists[i].id != 68) {
                            if (data.user_id == data.scientists[i].id && data.scientists[i].role == 5) {
                                console.log("Role 5 Myself", data.scientists[i].name);
                                options_cl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '-Myself</option>';
                            } else {
                                console.log("Role 5", data.scientists[i].name);
                                options_cl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '</option>';
                            }
                        }
                    }
                }
                else if (data.user_role == 6) {
                    // Role 6 users can see Role 6 users
                    for (var i = 0; i < data.scientists.length; i++) {
                        if (data.scientists[i].role == 6 && data.scientists[i].id != 68) {
                            if (data.user_id == data.scientists[i].id && data.scientists[i].role == 6) {
                                console.log("Role 6 Myself", data.scientists[i].name);
                                options_cl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '-Myself</option>';
                            } else {
                                console.log("Role 6", data.scientists[i].name);
                                options_cl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '</option>';
                            }
                        }
                    }
                }
                else if (data.user_role == 4) {
                    // Role 4 users can see Role 5 and 6 users
                    for (var i = 0; i < data.scientists.length; i++) {
                        if ((data.scientists[i].role == 6 || data.scientists[i].role == 5) && data.scientists[i].id != 68) {
                            if (data.user_id == data.scientists[i].id) {
                                console.log("Role 4 Myself", data.scientists[i].name);
                                options_cl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '-Myself</option>';
                            } else {
                                console.log("Role 4", data.scientists[i].name);
                                options_cl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '</option>';
                            }
                        }
                    }
                }
                else if (data.user_role == 3) {
                    // Role 3 users can see other Role 3 users
                    for (var i = 0; i < data.scientists.length; i++) {
                        if (data.scientists[i].role == 3 && data.scientists[i].id != 68) {
                            if (data.user_id == data.scientists[i].id && data.scientists[i].role == 3) {
                                console.log("Role 3 Myself", data.scientists[i].name);
                                options_cl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '-Myself</option>';
                            } else {
                                console.log("Role 3", data.scientists[i].name);
                                options_cl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '</option>';
                            }
                        }
                    }
                }
                else {
                    // Default case for other roles - show all available scientists
                    console.log("Unhandled user role:", data.user_role, "- showing all scientists");
                    for (var i = 0; i < data.scientists.length; i++) {
                        if (data.scientists[i].id != 68) {
                            console.log("Default role scientist:", data.scientists[i].name, "Role:", data.scientists[i].role);
                            options_cl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '</option>';
                        }
                    }
                }

                $("#scientistfilter_cl").html(options_cl);
                $("#scientistfilter_insight").html(options_cl);
                $("#scientistfilter_idea").html(options_cl);
                $("#scientistfilter_idea_cl").html(options_cl);
                $("#scientistfilter_insight_cl").html(options_cl);
               // $("#scientistfilter_evaluation").html(options_cl);
                $("#scientistfilter_evaluation_cl").html(options_cl);


                var options_tl = '';
                options_tl += '<option value="all">---All---</option>';

                console.log("Building TL options for user role:", data.user_role);

                if (data.user_role == 5) {
                    // Role 5 users can see Role 3 (TL) users
                    for (var i = 0; i < data.scientists.length; i++) {
                        if (data.scientists[i].role == 3) {
                            if (data.user_id == data.scientists[i].id && data.user_role == 5) {
                                console.log("TL Role 3 Myself for user role 5:", data.scientists[i].name);
                                options_tl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '-Myself</option>';
                            } else {
                                console.log("TL Role 3 for user role 5:", data.scientists[i].name);
                                options_tl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '</option>';
                            }
                        }
                    }
                }
                else if (data.user_role == 6) {
                    // Role 6 users can see Role 3 (TL) users
                    for (var i = 0; i < data.scientists.length; i++) {
                        if (data.scientists[i].role == 3) {
                            console.log("TL Role 3 for user role 6:", data.scientists[i].name);
                            options_tl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '</option>';
                        }
                    }
                }
                else if (data.user_role == 4) {
                    // Role 4 users can see Role 2 (Team Members) as TL
                    for (var i = 0; i < data.scientists.length; i++) {
                        if (data.scientists[i].role == 2) {
                            console.log("TL Role 2 for user role 4:", data.scientists[i].name);
                            options_tl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '</option>';
                        }
                    }
                }
                else if (data.user_role == 3) {
                    // Role 3 users can see Role 2 (Team Members)
                    for (var i = 0; i < data.scientists.length; i++) {
                        if (data.scientists[i].role == 2) {
                            console.log("TL Role 2 for user role 3:", data.scientists[i].name);
                            options_tl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '</option>';
                        }
                    }
                }
                else {
                    // Default case - show available TL options
                    console.log("Unhandled user role for TL:", data.user_role);
                    for (var i = 0; i < data.scientists.length; i++) {
                        if (data.scientists[i].role == 3 || data.scientists[i].role == 2) {
                            console.log("Default TL option:", data.scientists[i].name, "Role:", data.scientists[i].role);
                            options_tl += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '</option>';
                        }
                    }
                }

                $("#scientistfilter_cl").html(options_tl);
               $("#scientistfilter_insight_cl").html(options_tl);
                $("#scientistfilter_idea_cl").html(options_tl);
                $("#scientistfilter_evaluation_cl").html(options_tl);
               // $("#scientistfilter_cl").html(options_cl);


                var options_tm = '';
                options_tm += '<option value="all">---All---</option>';

                console.log("Building Team Member options for user role:", data.user_role);

                // Team Members are typically Role 2, but we also include special ID 68
                // Let's be more inclusive to ensure all team members are shown
                for (var i = 0; i < data.scientists.length; i++) {
                    console.log("Checking scientist:", data.scientists[i].name, "Role:", data.scientists[i].role, "ID:", data.scientists[i].id);

                    // Include Role 2 (Team Members) and special ID 68
                    // Also include Role 3 if current user is higher level (Role 4, 5, 6)
                    var shouldInclude = false;

                    if (data.scientists[i].role == 2 || data.scientists[i].id == 68) {
                        shouldInclude = true;
                        console.log("✓ Including Role 2 or Special ID:", data.scientists[i].name);
                    } else if (data.scientists[i].role == 3 && (data.user_role == 4 || data.user_role == 5 || data.user_role == 6)) {
                        shouldInclude = true;
                        console.log("✓ Including Role 3 for higher level user:", data.scientists[i].name);
                    }

                    if (shouldInclude) {
                        console.log("✓ Adding Team Member:", data.scientists[i].name, "Role:", data.scientists[i].role);
                        options_tm += '<option value="' + data.scientists[i].name + '" >' + data.scientists[i].name + '</option>';
                    } else {
                        console.log("✗ Skipping:", data.scientists[i].name, "Role:", data.scientists[i].role);
                    }
                }

                console.log("Final Team Member options HTML:", options_tm);

                $("#scientistfilter").html(options_tm);
                $("#scientistfilter_insight").html(options_tm);
                $("#scientistfilter_idea").html(options_tm);
                $("#scientistfilter_member_insight").html(options_tm);
                $("#scientistfilter_member_idea").html(options_tm);
                $("#scientistfilter_evaluation").html(options_tm);

                console.log("Team Member filters populated successfully");

            },
            error: function(xhr, status, error) {
                console.error("Error loading team members:", error);
                console.error("Response:", xhr.responseText);
            }
        });
    }
    get_scient_ready();
});


    function get_scient(){
        tab_value = $("#tab_value").val();
        $.ajax({
        type: "get",
        url: "<?php echo e(url('get_reporting_scientist_cl')); ?>",
        dataType: "json",
        success: function(data) {
            var options_cl = '';
                options_cl += '<option value="all">---All---</option>';
                for (var i = 0; i < data.scientists.length; i++) {
                        if ((data.scientists[i].role == 6 || data.scientists[i].role == 5) && (data.scientists[i].id != 68 )) {
                            if ((data.scientists[i].role == 5) && data.user_id != 68) {
                                options_cl += '<option value="' + data.scientists[i].name + '" >' + data
                                    .scientists[i].name + '-Myself</option>';
                            } else {
                                options_cl += '<option value="' + data.scientists[i].name + '" >' + data
                                    .scientists[i].name + '</option>';
                            }
                        }
                    }
                    
                if (tab_value == 3) {
                    $("#scientistfilter_idea_cl").html(options_cl);
                } else if(tab_value == 2) {
                    $("#scientistfilter_insight_cl").html(options_cl);
                } else if(tab_value == 0) {
                    $("#scientistfilter_evaluation_cl").html(options_cl);
                }
                else {
                    $("#scientistfilter_cl").html(options_cl);
                }
                var options_tl = '';
                options_tl += '<option value="all">---All---</option>';

                for (var i = 0; i < data.scientists.length; i++) {
                    if (data.scientists[i].role == 3 || data.user_id == data.scientists[i].id) {
                        if (data.user_id == data.scientists[i].id && data.user_role == 6) {
                            options_tl += '<option value="' + data.scientists[i].name + '" >' + data
                                .scientists[i].name + '-Myself</option>';
                        } else  if(data.scientists[i].role == 3){
                            options_tl += '<option value="' + data.scientists[i].name + '" >' + data
                                .scientists[i].name + '</option>';
                        }
                    }
                }
                if (tab_value == 3) {
                    $("#scientistfilter_idea").html(options_tl);
                } else if(tab_value == 2) {
                    $("#scientistfilter_insight").html(options_tl);
                } else if( tab_value == 0) {
                    $("#scientistfilter_evaluation").html(options_tl);
                    console.log(options_tl);
                    
                }
                else {
                    $("#scientistfilter").html(options_tl);
                    //console.log(options_tl);     
                }
                var options_tm = '';
                options_tm += '<option value="all">---All---</option>';
                for (var i = 0; i < data.scientists.length; i++) {
                    if ((data.scientists[i].role == 2 || data.scientists[i].id == 68)) {
                            options_tm += '<option value="' + data.scientists[i].name + '" >' + data
                                .scientists[i].name + '</option>';
                    }
                }
                if (tab_value == 3) {
                    $("#scientistfilter_member_idea").html(options_tm);  
                } else if(tab_value == 2) {
                    $("#scientistfilter_member_insight").html(options_tm);  
                } else if (tab_value == 0) {
                    $("#scientistfilter_evaluation").html(options_tm);  
                console.log(options_tm);
                }
                else {
                    $("#scientistfilter_member").html(options_tm);  
                }
        }
        });
    }

    function scient_cl() {
        tab_value = $("#tab_value").val();
        scientist_insight_cl = $('#scientistfilter_insight_cl').val()
        scientistfilter_idea_cl = $('#scientistfilter_idea_cl').val()
        scientistfilter_evaluation_cl = $('#scientistfilter_evaluation_cl').val()
        scientist_cl = $('#scientistfilter_cl').val();
        if (tab_value == 3) {
            scientist = scientistfilter_idea_cl;
        } else if(tab_value == 2) {
            scientist = scientist_insight_cl;
        } else if(tab_value == 0) {
            scientist = scientistfilter_evaluation_cl;
        }
        else {
            scientist = scientist_cl;
        }
        if(scientist == 'all'){
            get_scient();
        }else{
            $.ajax({
                type: "get",
                url: "<?php echo e(url('get_reporting_scientist')); ?>",
                data: {
                    "_token": "<?php echo e(csrf_token()); ?>",
                    'scientist': scientist,
                    'monthgoal': $('#monthgoal').val(),
                },
                success: function(data) {
                    var options_tl = '';
                    options_tl += '<option value="all">---All---</option>';
                    for (var i = 0; i < data.reporting_scientist.length; i++) {
                        if(data.reporting_scientist[i].role == 3 ){
                            options_tl += '<option value="' + data.reporting_scientist[i].name+ '" >' + data.reporting_scientist[i].name + '</option>';
                        }
                    }
                    if (tab_value == 3) {
                        $("#scientistfilter_idea").html(options_tl);
                    } else if(tab_value == 2) {
                        $("#scientistfilter_insight").html(options_tl);
                    } else if (tab_value == 0) {
                        $("#scientistfilter_evaluation").html(options_tl);
                    }
                    else {
                        $("#scientistfilter").html(options_tl);
                    }
                    var options_tm = '';
                    options_tm += '<option value="all">---All---</option>';
                    for (var i = 0; i < data.reporting_scientist.length; i++) {
                        if ((data.reporting_scientist[i].role == 2 || data.reporting_scientist[i].id == 68)) {
                                options_tm += '<option value="' + data.reporting_scientist[i].name + '" >' + data
                                .reporting_scientist[i].name + '</option>';
                        }
                    }
                    if (tab_value == 3) {
                        $("#scientistfilter_member_idea").html(options_tm);
                    } else if(tab_value == 2) {
                        $("#scientistfilter_member_insight").html(options_tm);
                    } else if (tab_value == 0) {
                        $("#scientistfilter_evaluation").html(options_tm);
                    }
                     else {
                        $("#scientistfilter_member").html(options_tm);
                    }  
                }
            });
            
        }
    }

    function  scientist_for_tl() {
        tab_value = $("#tab_value").val();
        scientistfilter_insight = $('#scientistfilter_insight').val()
        scientistfilter_idea = $('#scientistfilter_idea').val()
        scientistfilter = $('#scientistfilter').val();
        scientistfilter_evaluation = $('#scientistfilter_evaluation').val();
        
        if (tab_value == 3) {
            scientist = scientistfilter_idea;
        } else if(tab_value == 2) {
            scientist = scientistfilter_insight;
        } else if (tab_value == 0) {
            scientist = scientistfilter_evaluation;
        }
        else {
            scientist = scientistfilter;
        }
        
        if(scientist == 'all'){
            
            get_scient(); 
        }else{
            $.ajax({
            type: "get",
            url: "<?php echo e(url('scientist_for_tl')); ?>",
            data: {
                "_token": "<?php echo e(csrf_token()); ?>",
                'scientist': scientist,
                'monthgoal': $('#monthgoal').val(),
            },
            success: function(data) {
                var options_tm = '';
                options_tm += '<option value="all">--All--</option>';
                for (var i = 0; i < data.reporting_scientist.length; i++) {
                    if ((data.reporting_scientist[i].role == 2 || data.reporting_scientist[i].id == 68)) {
                            options_tm += '<option value="' + data.reporting_scientist[i].name + '" >' + data
                            .reporting_scientist[i].name + '</option>';
                    }
                }
                if (tab_value == 3) {
                    $("#scientistfilter_member_idea").html(options_tm);
                } else if(tab_value == 2) {
                    $("#scientistfilter_member_insight").html(options_tm);
                } else if (tab_value == 0) {
                    $("#scientistfilter_evaluation").html(options_tm);
                }
                 else {
                    $("#scientistfilter_member").html(options_tm);
                }
            }
            });
        }
    }

    function scientist_percentage(this_id,key,value){
        $("#percentage_form")[0].reset();
        $("#percentage_id").val(this_id);
        $("#percentage_key").val(key);
        if(value =='-' || value ==''){
        }else{
            $("#percentage").val(value);
        }
        
    }
    function validateScientistPercentage(inputElement) {
        var inputValue = inputElement.value.trim(); 
        var validInput = inputValue.replace(/[^\d]/g, '');
        validInput = validInput.substring(0, 3);
        if (parseInt(validInput) > 100) {
            validInput = '100';
        }
        if (validInput.startsWith('0')) {
            if (validInput !== '0') {
                validInput = validInput.replace(/^0+/, '');
            }
        }
        inputElement.value = validInput;
    }
    function percentage_add(){
        if (($('#percentage').val() == '')) {
            $.alert({
                title: 'Invalid',
                content: 'Please fill the percentage field',
            });
        } else {
            var percentageFormData = $('#percentage_form').serialize();
            $.ajax({
                type: "post",
                url: "<?php echo e(url('addscientistpercentage')); ?>",
                data: percentageFormData,
                success: function(data) {
                    if (data == 1) {
                        $('#add_percentage_modal').modal('hide');

                        Swal.fire({
                            position: 'top-end',
                            icon: 'success',
                            title: 'Percentage Updated Successfully',
                            showConfirmButton: false,
                            customClass: 'swal-wide',
                            timer: 1500
                        });
                        var activeTabId = $('.nav-tabs .nav-link.active').attr('id');
                        localStorage.setItem('activeTabIndex', activeTabId);
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    }

                }
            });
        }
    }
    $("#create_btn_ideas").click(function(){
        document.getElementById("learning_devlopment_idea").reset();
        //$("#idea_insight").val("");
        $("#idea_cate").val("");
        $("#idea_cate").selectedIndex = 0;
        $("#idea_cate").trigger("change");
        $("#idea_insight").val("");
        $("#idea_insight").trigger("change");
        $('#idea_category').val("");
        $('#idea_category').trigger("change");
        $('#squeeze_idea').val("");
        $('#squeeze_idea').trigger("change");
        $("#product_category").val("");
        $("#product_category").trigger("change");
        $("#product_brand").val("");
        $("#product_brand").trigger("change");
        $("#replicationcompetitors").val("");
        $("#replicationcompetitors").trigger("change");
        $("#publicationscope").val("");
        $("#publicationscope").trigger("change");
        $("#ideaweight").val("");
        $("#ideaweight").trigger("change");
        $("#squees_ideastatus_squees").val("");
        $("#squees_ideastatus_squees").trigger("change");
        $("#ideastatusproduct").val("");
        $("#ideastatusproduct").trigger("change");
        $("#method_ideastatus_squees").val("");
        $("#method_ideastatus_squees").trigger("change");
        $("#research_ideastatus_research").val("");
        $("#research_ideastatus_research").trigger("change");
        $(".iiy_form_field_hide").hide();
        get_insight_idea_list();
    });

    $('#childideastatus').change(function(e){
    if($(this).val()=='initiated'){
        $('#mention_milestone_div').removeClass('d-none');
        $('#reason_not_initiated_div').addClass('d-none');
        $('#significant_impact_div').addClass('d-none');

    }
    else if($(this).val()=='not_initiated'){
        $('#reason_not_initiated_div').removeClass('d-none');
        $('#mention_milestone_div').addClass('d-none');
        $('#significant_impact_div').addClass('d-none');
    }
    else if($(this).val()=='completed'){
        $('#significant_impact_div').removeClass('d-none');
        $('#mention_milestone_div').addClass('d-none');
        $('#reason_not_initiated_div').addClass('d-none');
    }
    else{
        $('#mention_milestone_div').addClass('d-none');
        $('#reason_not_initiated_div').addClass('d-none');
        $('#significant_impact_div').addClass('d-none');
    }
    });

    $('#parentideastatus').change(function(e){
    if($(this).val()=='initiated'){
        $('#mention_milestone_div').removeClass('d-none');
        $('#reason_not_initiated_div').addClass('d-none');
        $('#significant_impact_div').addClass('d-none');
    }
    else if($(this).val()=='not_initiated'){
        $('#reason_not_initiated_div').removeClass('d-none');
        $('#mention_milestone_div').addClass('d-none');
        $('#significant_impact_div').addClass('d-none');
    }
    else if($(this).val()=='completed'){
        $('#significant_impact_div').removeClass('d-none');
        $('#mention_milestone_div').addClass('d-none');
        $('#reason_not_initiated_div').addClass('d-none');
    }
    else{
        $('#mention_milestone_div').addClass('d-none');
        $('#reason_not_initiated_div').addClass('d-none');
        $('#significant_impact_div').addClass('d-none');
    }
    });

   

    $('#hours_spent').on('input', function () {
        let value = $(this).val();
        value = value.replace(/\D/g, '').slice(0, 4);
        $(this).val(value);
    });

    $('#source_learning').select2({
        dropdownParent: $('#add_learning_devlopment_modal')
    });
    $('#area_of_development').select2({
        dropdownParent: $('#add_learning_devlopment_modal')
    });
    $('#topic_learnt_create_insight').select2({
        dropdownParent: $('#add_insights_modal')
    });
    $('#insight_category').select2({
        dropdownParent: $('#add_insights_modal')
    });
    $('#scientist').select2({
        dropdownParent: $('#add_insights_modal')
    });
    $('#edit_topic_learnt').select2({
        dropdownParent: $('#edit_insights_modal')
    });
    $('#edit_insight_category').select2({
        dropdownParent: $('#edit_insights_modal')
    });
   
    

function editL_D_insight(insightId) {

    let _token = $("#csrf_token").val();

    $.ajax({
        url: "<?php echo e(url('get-insight-details')); ?>",
        type: "GET",
        dataType: "json",
        data: { id: insightId, _token: _token },
        success: function(response) {

            if (response.length) {
                let data = response[0];

                $("#edit_insight_id").val(data.id);
                $("#edit_insight_topic").val(data.insights_generated);
                $("#edit_insight_category").val(data.insights_category).trigger("change");
                $("#edit_scientist_insights").val(data.scientist);
                $("#edit_insight_remarks").val(data.remark);
            } else {
                alert("No data found for this insight.");
            }
        },
        error: function(xhr) {
            console.error("Error fetching data:", xhr.responseText);
        }
    });
}

$('#edit_idea_insight').change(function () {
    var selectedInsights = $(this).val();  
    var authUserId = <?php echo e(Auth::user()->id); ?>;  
    
    $.ajax({
        type: "POST",
        url: "<?php echo e(url('get_scientist_insight')); ?>",
        data: {
            idea_insight: selectedInsights,  
            _token: "<?php echo e(csrf_token()); ?>",
            auth_user_id: authUserId
        },
        success: function (data) {          
            var scientistDropdownEdit = $('#edit_idea_scientist');
            scientistDropdownEdit.empty().append('<option value="0">Select</option>');

            if (Array.isArray(data) && data.length > 0) {
                var selectedScientists = new Set();

                data.forEach(function (item) {
                    if (item.scientist_name !== null) {
                        scientistDropdownEdit.append(
                            `<option value="${item.scientist_id}">${item.scientist_name}</option>`
                        );
                        selectedScientists.add(item.scientist_id);
                    }
                });

                if (scientistDropdownEdit.find('option').length > 1) {
                    scientistDropdownEdit.val(scientistDropdownEdit.find('option:eq(1)').val()).trigger('change');
                }

            } 
        },
        error: function (xhr, status, error) {
        }
    });
});




function editL_D_idea(id) {
    let ideaId = id;      
    let _token = $("#csrf_token").val();

    $.ajax({
        url: "<?php echo e(url('get-idea-details')); ?>",
        type: 'GET',
        dataType: 'json',
        data: { id: ideaId, _token: _token },
        success: function(response) {
            
            if (response) {
                let idea = response.idea;
                let allInsights = response.all_insights;
                let selectedInsights = response.selected_insights;

                $("#edit_idea_id").val(idea.id);
                if ($("#edit_idea_category option[value='" + idea.idea_category + "']").length === 0) {
                    $("#edit_idea_category").append(new Option(idea.idea_category, idea.idea_category));
                }
                $("#edit_idea_category").val(idea.idea_category);

                if ($("#edit_idea_type option[value='" + idea.idea_type + "']").length === 0) {
                    $("#edit_idea_type").append(new Option(idea.idea_type, idea.idea_type));
                }
                $("#edit_idea_type").val(idea.idea_type).trigger("change");
                $("#edit_idea_insight").val(idea.idea_insight);
                $("#edit_idea_txt").val(idea.idea_txt);
                $("#edit_ideas_comments").val(idea.ideas_comments);
                $("#edit_target_date").val(idea.target_date).trigger("change");
                
                let scientistOptions = '';
                if (idea.scientists && idea.scientists.length > 0) {
                    idea.scientists.forEach(scientist => {
                        scientistOptions += `<option value="${scientist.id}" selected>${scientist.name}</option>`;
                    });
                }
                $("#edit_idea_scientist").empty().append(scientistOptions).trigger("change.select2");

                let insightOptions = '';
                allInsights.forEach(insight => {
                    let isSelected = selectedInsights.includes(insight.id) ? 'selected' : '';
                    insightOptions += `<option value="${insight.id}" ${isSelected}>${insight.insights_generated}</option>`;
                });

                $("#edit_idea_insight").empty().append(insightOptions).trigger("change.select2");
                $("#edit_idea_insight").trigger('change');
                $("#edit_preferedconcepts_modal").modal("show");
            }
        },
        error: function () {
            alert("Error fetching data.");
        }
    });
}

$("#edit_learning_development_insights").on("submit", function (e) {
    e.preventDefault();

    let formData = new FormData(this); 
    let _token = $("#csrf_token").val();
 
    formData.append("_token", _token);
    let scientistInsights = $("#edit_scientist_insights").val() || [];
    formData.append("edit_scientist_insights", JSON.stringify(scientistInsights));
    $.ajax({
    url: "<?php echo e(url('update-insight')); ?>",
    type: "POST",
    contentType: "application/json",
    data: JSON.stringify({
        edit_insight_id: $("#edit_insight_id").val(),
        edit_insight_topic: $("#edit_insight_topic").val(),
        edit_insight_category: $("#edit_insight_category").val(),
        edit_scientist_insights: $("#edit_scientist_insights").val() || "", 
        edit_insight_remarks: $("#edit_insight_remarks").val(),
        _token: $("#csrf_token").val()
    }),
    success: function (response) {
        if (response.success) {
            Swal.fire({
                position: 'top-end',
                icon: 'success',
                title: 'Updated Successfully',
                showConfirmButton: false,
                timer: 1500
            });
            $('#edit_insights_modal').modal('hide');
            $("#insights_devlopment_table").DataTable().ajax.reload();
        } else {
            alert("Error: " + response.message);
        }
    },
    error: function (xhr) {
        console.log("AJAX Error:", xhr.responseText);
        alert("AJAX Error: " + xhr.responseText);
    }
});
});

function update_idea(e) {

    let id = $("#edit_idea_id").val(); 
    let ideaTypeText = $("#edit_idea_type option:selected").text();

    let formData = {
        edit_idea_id: id,
        edit_idea_category: $("#edit_idea_category").val(),
        edit_idea_type: ideaTypeText,
        edit_idea_insight: $("#edit_idea_insight").val(), 
        edit_idea_txt: $("#edit_idea_txt").val(),
        edit_idea_scientist: $("#edit_idea_scientist").val(), 
        edit_ideas_comments: $("#edit_ideas_comments").val(),
        edit_target_date: $("#edit_target_date").val(),
        _token: $("#csrf_token").val(), 
    };

    $.ajax({
        type: "POST",
        url: "<?php echo e(url('update_idea')); ?>",
        data: formData,
        dataType: "json",
        success: function (response) {
            if (response.success) {
                Swal.fire({
                    position: "top-end",
                    icon: "success",
                    title: "Idea Updated Successfully",
                    showConfirmButton: false,
                    timer: 1500
                });

                $("#edit_preferedconcepts_modal").modal("hide"); 
                $("#learning_devlopment_idea_table").DataTable().ajax.reload(null, false);
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Update Failed",
                    text: response.message || "Please check the inputs and try again."
                });
            }
        },
        error: function (xhr) {
            Swal.fire({
                icon: "error",
                title: "Update Failed",
                text: xhr.responseJSON?.message || "Something went wrong."
            });
        }
    });
}

  function validateSkillLevel(input) {
        if (input.value > 5) {
            input.value = 5;
        } else if (input.value < 1) {
            input.value = 1;
        }
    }

</script>



<?php /**PATH E:\kumaran\Primus-LandD-Packaging\resources\views/learning_development.blade.php ENDPATH**/ ?>