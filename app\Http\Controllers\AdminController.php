<?php
namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\DataTables;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Objective;
use App\Models\Division;
use App\Models\Bussiness;
use App\Models\Problemsolved;
use App\Models\Majorsetbacks;
use App\Models\Achivements;
use App\Models\formulation;
use App\Models\Taskinfo;
use App\Models\Taskinfo_month;
use App\Models\Taskinfo_week;
use App\Models\CFR;
use App\Models\Feedback;
use App\Models\Recognition;
// use App\Models\Conversationnew;
use App\Models\Master_table;
use App\Models\MainCategory;
use App\Models\InsightCategory;
use App\Models\ParentIdea;
use App\Models\SubCategory;
use App\Models\ProductCategory;
use App\Models\ReplicationCompetitors;
use App\Models\PublicationScope;
use App\Models\IdeaCategory;
use App\Models\Brand;
use App\Models\TargetSegment;
use App\Models\GoalAchivementdata;
use App\Models\Productivityoutsourcing;
use App\Models\LanddInsight;
use App\Models\LearningDevelopment;
use App\Models\Completitioninformation;
use App\Models\SupportRequired;
use App\Models\InfrastructureDevelopment;
use App\Models\HumanResources;
use App\Models\StatutoryCompliances;
use App\Models\KnowledgeEnhancement;
use App\Models\GoalAchivement;
use App\Models\TestSample;
use App\Models\Worktype;
use App\Models\Subworktype;
use App\Models\Category;
use App\Models\Sampletest;
use App\Models\LearningDevelopmentIdea;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;



use Arr;
use DB;
use Session;
use Auth;
use DateTime;
use QueryException;
class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        /*  $role = Session::get('role');
         echo "<pre>"; print_r($role); exit;
         if($role != '1'){
            //Auth::logout();
            return redirect('/login');
         } */
    }
    public function dashboard()
    {
        return view('learning_development');
        /*   $user_list = User::where('status',1)->get();
        return view('admin/dashboard')
        ->with('user_list',$user_list); */
    }
    public function crf()
    {
        return view('admin/conversation');
    }
    public function  masterachivement()
    {
        if(Auth::user()->role != 5)
        {
            $user_list_info = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->get();
        }else
        {
            $user_list_info = User::where('status', 1)->where('is_supervisor', "1")->get();
        }
        $user_listonemoreteam = User::where('status', 1)->where('role', 2)->where('supervisor_id', Auth::user()->supervisor_id)->get();
        // dd($user_listonemoreteam);
        
        $supervisor_list = User::where('is_supervisor', 1)->get();
        $division_list = Division::where('status', 1)->get();
        $division = Division::get();
        if (Auth::user()->role == 5) {
            $scientist = User::where('status', 1)->get();
        }else{
            $myteams = DB::table('users')->where('status', 1)->where('supervisor_id', Auth::id())->get();
            $myteams_tm = DB::table('users')->where('status', 1)->whereIn('supervisor_id', $myteams->pluck('id'))->get();
            $scientist = User::where('status', 1)->whereIn('id', $myteams->pluck('id')->merge($myteams_tm->pluck('id'))->all())->orWhere('id', Auth::id())->get();
        }
        $user_list = User::where('status',1)->where('division_id', Auth::user()->division_id)->get();
        $competition_informations =DB::table('tbl_competition_information')->get();
        if (Auth::user()->role == 5 ) {
            $division_list = Division::where('status', 1)->get();
        } else { 
        $user_id = Auth::user()->id;
        $user_division_id = Auth::user()->division_id;
        $division = User::where('supervisor_id', $user_id)->where('status', 1)->pluck('division_id')->toArray();
        $division[] = $user_division_id;
        $division_list = Division::where('status', 1)->whereIn('id', $division)->get();
        }
        $achive_count = DB::table('tbl_specific_achievements')->where('deleted_status', 0)->count();
        $analysis_count = DB::table('tbl_analysis_others')->where('deleted_status', 0)->count();
        $cost_count = DB::table('tbl_cost_others')->where('deleted_status', 0)->count();
        $formulation_count = DB::table('tbl_formulation_others')->where('deleted_status', 0)->count();
        $signature_count = DB::table('tbl_signature_others')->where('deleted_status', 0)->count();
        return view('admin/mastersachivement')
            ->with('user_list', $user_list)
            ->with('user_listonemoreteam', $user_listonemoreteam)
            ->with('supervisor_list', $supervisor_list)
            ->with('scientist', $scientist)
            ->with('scientist_goalachivement', $scientist_goalachivement)
            ->with('goalachive', $goalachive)
            ->with('goalachivepr', $goalachivepr)
            ->with('user_role', $user_role)
            ->with('division_list',$division_list)
            ->with('scientist_info',$user_list_info) ;
    }

    
    public function users()
    {
        $user_list = User::where('status', 1)->get();
        $supervisor_list = User::where('is_supervisor', 1)->get();
        $division_list = Division::where('status', 1)->get();
        return view('admin/users')
            ->with('user_list', $user_list)
            ->with('division_list', $division_list)
            ->with('supervisor_list', $supervisor_list);
    }
    public function masters()
    {
        $user_list = User::where('status', 1)->get();
        $supervisor_list = User::where('is_supervisor', 1)->get();
       
        $insight_category_list = InsightCategory::all();
        $user_role = DB::table('user_role')->get();
        return view('admin/masters')
            ->with('user_list', $user_list)
           
            ->with('supervisor_list', $supervisor_list)
            ->with('insight_category_list', $insight_category_list)
            ->with('user_role', $user_role);
    }
    public function messages()
    {
        $user_list = User::where('status', 1)->get();
        return view('admin/messages')
            ->with('user_list', $user_list);
    }
    public function events()
    {
        $user_list = User::where('status', 1)->get();
        return view('admin/events')
            ->with('user_list', $user_list);
    }
    /*  public function cfr()
    {
        $user_list = User::where('status',1)->get();
        return view('admin/cfr')
        ->with('user_list',$user_list);
    } */
    public function save_yearly_form(Request $request)
    {
        $obj_count = Objective::count();
        if ($obj_count > 0) {
            $latest_obj = DB::table('tbl_objective_master')->latest('id')->first();
            $obj_id = $latest_obj->obj_id + 1;
        } else {
            $obj_id = 1;
        }
        $user_id = Auth::user()->id;
        $user_list = explode(',', $request->user);
        foreach ($user_list as $user) {
            $data = array(
                'division_id' => $request->division,
                'year' => $request->year,
                'objective' => $request->objective,
                'user' => $user,
                'status' => 1,
                'created_by' => $user_id,
                'obj_id' => $obj_id,
                'created_date' => date('Y-m-d H:i:s'),
            );
            Objective::insert($data);
        }
        return json_encode("success");
    }
    public function add_new_user(Request $request)
    {
        $mcount = User::where('email', $request->user_email)->count();
        if ($mcount == 0) {
            $data = array(
                'name' => $request->user_name,
                'email' => $request->user_email,
                'password' => Hash::make($request->user_password),
                'role' => $request->user_role,
                'status' => 1,
            );
            User::insert($data);
            return json_encode("success");
            //return view('user');
        } else {
            return json_encode("failed");
        }
    }
    public function save_goal_achivements(Request $request)
    {
        if (Auth::user()->role == 2) {
            for ($i = 0; $i < 7; $i++) {
                $parameter = "parameter" . $i;
                $target = "target_part" . $i;
                $actual = "actual_part" . $i;
                $status = "status_part" . $i;
                $month = $request->month . "-01";
                $goalachivecount = GoalAchivementdata::where('input_month', $month)->where('parameter_id', $request->$parameter)->where('scientist_id', Auth::user()->id)->count();
                if ($goalachivecount != 0) {
                    $update_goal_data = [
                        'target' => $request->$target,
                        'actual' => $request->$actual,
                        'status' => $request->$status,
                    ];
                    GoalAchivementdata::where('input_month', $month)->where('parameter_id', $request->$parameter)->where('scientist_id', Auth::user()->id)->update($update_goal_data);
                } else {
                    $data = array(
                        'parameter_id' => $request->$parameter,
                        'target' => $request->$target,
                        'actual' => $request->$actual,
                        'status' => $request->$status,
                        'scientist_id' => Auth::user()->id,
                        'input_month' => $request->month . "-01",
                    );
                    GoalAchivementdata::insert($data);
                }
            }
        } else if (Auth::user()->role != 2) {
            for ($i = 0; $i < 7; $i++) {
                $parameter = "parameter" . $i;
                $target = "target_part" . $i;
                $actual = "actual_part" . $i;
                $status = "status_part" . $i;
                $month = $request->month . "-01";
                $goalachivecount = GoalAchivementdata::where('input_month', $month)->where('parameter_id', $request->$parameter)->where('scientist_id', $request->scientist_ld)->count();
                if ($goalachivecount != 0) {
                    $update_goal_data = [
                        'target' => $request->$target,
                        'actual' => $request->$actual,
                        'status' => $request->$status,
                    ];
                    GoalAchivementdata::where('input_month', $month)->where('parameter_id', $request->$parameter)->where('scientist_id', $request->scientist_ld)->update($update_goal_data);
                } else {
                    $data = array(
                        'parameter_id' => $request->$parameter,
                        'target' => $request->$target,
                        'actual' => $request->$actual,
                        'status' => $request->$status,
                        'scientist_id' =>  $request->scientist_ld,
                        'input_month' => $request->month . "-01",
                    );
                    GoalAchivementdata::insert($data);
                }
            }
        }
        return response('1');
        //return view('user');
    }
    public function get_month_goal_achivements(Request $request)
    {
        if (Auth::user()->role == 2) {
            $month = $request->month . "-01";
            $goalachivecount = GoalAchivementdata::where('input_month', $month)->where('scientist_id', Auth::user()->id)->get();
        } else  if (Auth::user()->role != 2) {
            $month = $request->month . "-01";
            $goalachivecount = GoalAchivementdata::where('input_month', $month)->where('scientist_id', $request->scientist_ld)->get();
        } else {
            $month = $request->month . "-01";
            $goalachivecount = GoalAchivementdata::where('input_month', $month)->where('scientist_id', Auth::user()->id)->get();
        }
        $result  = array();
        $result['data1'] = $goalachivecount;
        return   json_encode($result);
    }

    public function get_reporting_scientist(Request $req)
    {
        if(($req->month_bussiness =='2023-03')||($req->month_bussiness =='2023-02')){
            $table ='users_old' ;
        }else{
            $table = 'users';
        }
        $reporting_scientist_id = DB::table($table)->where('name', $req->scientist)->first();
        if ($reporting_scientist_id) {
            $reporting_scientist_tl = DB::table($table)
                ->where('supervisor_id', $reporting_scientist_id->id)
                ->where('status', 1)
                ->select('id', 'name', 'role', 'supervisor_id')
                ->get();
        } else {
            $reporting_scientist_tl = collect(); // empty collection to avoid further errors
        }
                // dd($reporting_scientist_tl);
        $reporting_scientist = DB::table($table)->whereIn('supervisor_id', $reporting_scientist_tl->pluck('id'))->where('status', 1)->select('id','name','role','supervisor_id')->get();
        $combined_reporting_scientists = $reporting_scientist_tl->concat($reporting_scientist);
        $user_id =Auth::user()->id; 
        $data = [
            'reporting_scientist' => $combined_reporting_scientists,
            'user_id' => $user_id,
        ];
        return $data;
    }
    public function get_all_user(Request $req)
    {
        // try {
        /* $get_all_user = DB::table('users')
                            ->join('users', 'users.supervisor_id', '=', 'users.id')
                            ->select('users.*', 'users.name as sup_name')
                            ->orderBy('users.id', 'desc')
                            ->get(); */
        $get_all_user = User::where('status', 1)->get();
        return Datatables::of($get_all_user)
            ->addColumn('user_id', function ($get_all_user) {
                $user_id = '';
                $user_id .= $get_all_user->id;
                return $user_id . '';
            })
            ->addColumn('user_name', function ($get_all_user) {
                $user_name = '';
                $user_name .= ucfirst($get_all_user->name);
                return $user_name . '';
            })
            ->addColumn('user_email', function ($get_all_user) {
                $user_email = '';
                $user_email .= $get_all_user->email;
                return $user_email . '';
            })
            ->addColumn('user_division', function ($get_all_user) {
                $user_division = '';
                $division_name = Division::where('id', $get_all_user->division_id)->first();
                if (!empty($division_name)) {
                    $user_division .= $division_name->division_name;
                } else {
                    $user_division .= '-';
                }
                return $user_division . '';
            })
            ->addColumn('supervisor', function ($get_all_user) {
                $supervisor = '';
                if ($get_all_user->supervisor_id != NULL) {
                    $supervisor_name = User::where('id', $get_all_user->supervisor_id)->first();
                    $supervisor = $supervisor_name->name;
                } else {
                    $supervisor = "-";
                }
                //$supervisor .= $get_all_user->supervisor_id;
                return $supervisor . '';
            })
            ->addColumn('role', function ($get_all_user) {
                $role = '';
                $role_name = DB::table('user_role')->where('id', $get_all_user->role)->first();
                $role = $role_name->name;
                return $role . '';
            })
            ->addColumn('action', function ($get_all_user) {
                $action = '';
                $action .= ' <button class="btn btn-sm btn-success" onClick="edit_user(' . $get_all_user->id . ')" ><i class="fa fa-edit"></i></button>
                <button class="btn btn-sm btn-danger" onClick="delete_user(' . $get_all_user->id . ')"><i class="fa fa-trash"></i></button>
                ';
                return $action . '';
            })
            ->addColumn('status', function ($get_all_user) {
                $status = '';
                if ($get_all_user->status == '1') {
                    $status .= '<select style="border:none; color:blue;" onchange="update_user_status(' . $get_all_user->id . ',2)">
                         <option selected="selected" hidden style="border:none; color:blue;">Active</option>
                         <option style="border:none; color:red;">In-Active</option>
                         </select>';
                } else {
                    $status .= '<select style="border:none; color:red;" onchange="update_user_status(' . $get_all_user->id . ',1)">
                         <option selected="selected" hidden style="border:none; color:blue;">In-Active</option>
                         <option style="border:none; color:blue;">Active</option>
                         </select>';
                }
                return $status . '';
            })
            ->rawColumns(array("user_id", "user_name", "user_email", "user_division", "supervisor", "role", "status", "action"))
            ->make(true);
        //  } catch (QueryException $e) {
        //     echo "Bad Request";
        //     dd();
        // }
    }
  

    public function update_users(Request $request)
    {
        $email_count = User::where('email', '=', $request->user_email)->where('id', '!=', $request->user_id)->get()->count();
        // dd($request->all());
        if ($email_count == 0) {
            $update_user_data = [
                'name' =>  $request->user_name,
                'division_id' =>  $request->user_division,
                'email' =>  $request->user_email,
                'supervisor_id' =>  $request->user_supervisor,
                'is_supervisor' =>  $request->user_is_supervisor,
                'role' =>  $request->user_role,
                'role_change' =>  $request->user_role,
            ];
            User::where('id', $request->user_id)->update($update_user_data);
            $data = [
                'message' => "User Updated Successfully!",
                "action" => "success"
            ];
            return response()->json($data);
            return $update_user_data;
        } else {
            $data = [
                'message' => "Duplicate User ",
                "action" => "warning"
            ];
            return response()->json($data);
        }
    }
    public function delete_user(Request $request)
    {
        // $email_count = User::where('email', '=', $request->user_email)->where('id', '!=', $request->user_id)->get()->count();
        // dd($request->all());
        $del_user_data = [
            'status' => 0,
        ];
        User::where('id', $request->user_id)->update($del_user_data);
        $data = [
            'message' => "User Deleted Successfully!",
            "action" => "success"
        ];
        return response()->json($data);
        return $del_user_data;
    }
   
    public function admin_approval(Request $request)
    {
        $data = array(
            'status' => 1,
        );
        DB::table('tbl_task_info_weekly')->where('id', $request->task_id)->update($data);
        return json_encode("ok");
    }
    public function admin_approval_key_result(Request $request)
    {
        $data = array(
            'status' => 1,
        );
        DB::table('tbl_task_info')->where('id', $request->task_id)->update($data);
        return json_encode("ok");
    }
    
    public function save_new_user(Request $request)
    {
        $count = User::where('email', $request->email)->count();
        if ($count == 0) {
            // $user_id = Auth::user()->id;
            $curent_date = date('Y-m-d H:i:s');
            $data = array(
                'name' => $request->name,
                'email' => $request->email,
                'division_id' => $request->division,
                'supervisor_id' => $request->supervisor_id,
                'is_supervisor' => $request->is_supervisor,
                'role' => $request->role,
                'role_change' => $request->role,
                'password' => Hash::make('12345678'),
                'created_at' => $curent_date,
                'status' => 1,
            );
            User::insert($data);
            $rdata = "success";
        } else {
            $rdata = "fail";
        }
        return json_encode($rdata);
    }
    
    public function getusername($id)
    {
        $user_name = DB::table('users')
            ->where('id', $id)
            ->get();
        return $user_name[0]->name;
    }
   
    

    public function edit_learning_development(Request $request) {
        $l_dlist = DB::table('tbl_learning_devlopment')->where('id', $request->id)->first();
       // dd($l_dlist);
    
        return response()->json([
            'topics_learnt' => $l_dlist->topics_learnt ?? '',
            'topics_present' => $l_dlist->topics_present ?? '',
            'hours_invested' => $l_dlist->no_of_hours_invested_in_iiy ?? '',
            'vendor_facility_visited' => $l_dlist->no_of_vendor_facility_visited ?? '',
            'conferences_attended' => $l_dlist->no_of_conference_attended ?? '',
            'exhibitions_attended' => $l_dlist->no_of_exhibitions_attended ?? '',
            'consumer_retail_visits' => $l_dlist->no_of_consumer_and_retail_visits ?? '',
            'ideas_generated' => $l_dlist->no_of_ideas_generated_through_iiy ?? '',
            'uploaded_files' => collect(json_decode($l_dlist->upload_the_presentation_weekly))
            ->map(function ($file) {
                return asset('storage/presentations/' . $file);
            })  ,
            'date_range_start' => $l_dlist->date_range_start ?? '',
            'date_range_end' => $l_dlist->date_range_end ?? ''   
           ]);
    }
   


    public function update_learning_development(request $request){
         $data = array( 'number' => $request->number);
         $addform_result=DB::table('tbl_learning_devlopment')->where('id',$request->id)->update($data);
         $result = array( "response" => "success", ); echo json_encode($result);
        }
        
        public function view_learning_development(request $request){
            $l_dlist = DB::table('tbl_learning_devlopment')->get();
            return json_encode($l_dlist);
        }

    public function fetch_insight_idea_list(Request $request)
    {
        
        if (Auth::user()->role == 1) {
            $get_all_insight =  LanddInsight::where('deleted_status','0')
            ->get();
         } else if (Auth::user()->role == 5) {
            $get_all_insight =  LanddInsight::where('deleted_status','0')
            ->get();
         } else if (Auth::user()->role == 4) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $get_all_insight =  LanddInsight::whereIn('scientist', $user_listid)->where('deleted_status','0')
            ->get();
         } else if (Auth::user()->role == 6) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $get_all_insight =  LanddInsight::whereIn('scientist', $user_listid)->where('deleted_status','0')
            ->get();
         } else  if (Auth::user()->role == 3) {
            $user_listid = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $get_all_insight =   LanddInsight::whereIn('scientist', $user_listid)->where('deleted_status','0')
            ->get();
        } else {
            
            $user_listid = User::where('status', 1)->where('supervisor_id', Auth::user()->supervisor_id)->orWhere('id', Auth::id())->pluck('id')->toArray();
            array_push($user_listid,Auth::user()->supervisor_id);
            // dd($user_listid);
            $get_all_insight =   LanddInsight::whereIn('scientist', $user_listid)->where('deleted_status','0')
            ->get();

         //   $get_all_insight =   LanddInsight::where('scientist', Auth::user()->id)->where('deleted_status','0')->get();
        }


          echo json_encode($get_all_insight);

    }

   


   public function save_learning_devlopment_idea(Request $request)
{
    $data = $request->all();
    //dd($data);
    $authUserId = Auth::id();
    $created_date = Carbon::parse($data['target_date'])->startOfMonth()->format('Y-m-d');

    $scientistIds = isset($data['scientist']) ? (array) $data['scientist'] : [];
    if (empty($scientistIds)) {
        $scientistIds[] = $authUserId;  
    }

    if (isset($data['created_by']) && $data['created_by'] == $authUserId) {
        if (!in_array($authUserId, $scientistIds)) {
            $scientistIds[] = $authUserId; 
        }
    }    
   


    $learning_development_idea = LearningDevelopmentIdea::create([
        'idea_insight' => json_encode($data['idea_insight'] ?? []),
        'idea_txt' => $data['idea_txt'] ?? null,
        'idea_type' => $data['idea_type'] ?? null,
        'idea_category' => $data['idea_category'] ?? 1,
        'scientist' => json_encode($scientistIds), 
        'ideas_comments' => $data['ideas_comments'] ?? null,
        'created_date' => $created_date,
        'target_date' => $data['target_date'],
        'created_by' => $authUserId,
        'updated_at' => now(),
        'created_current_date' => now(),
        
    ]);

    return response()->json(1);        
}

    
public function get_learning_devlopment_idea_list(Request $req)
{
    $get_my_learning_devlopment_idea = LearningDevelopmentIdea::query()->where('deleted_status', '0');

    // Filter by CL (Client/Lead)
    if ($req->scientistfilter_cl != 'all' && $req->scientistfilter_cl != '') {
        $cl_id = User::where('name', $req->scientistfilter_cl)->pluck('id')->first();
        if ($cl_id) {
            $get_my_learning_devlopment_idea = $get_my_learning_devlopment_idea->whereRaw('JSON_CONTAINS(scientist, ?)', [json_encode($cl_id)]);
        }
    }

    // Filter by TL (Team Lead)
    if ($req->scientistfilter_tl != 'all' && $req->scientistfilter_tl != '') {
        $tl_id = User::where('name', $req->scientistfilter_tl)->pluck('id')->first();
        if ($tl_id) {
            $get_my_learning_devlopment_idea = $get_my_learning_devlopment_idea->where('scientist', 'like', '%"' . $tl_id . '"%');
        }
    }

    // Filter by Team Member
    if ($req->scientistfilter_member != 'all' && $req->scientistfilter_member != '') {
        $user_id = User::where('name', $req->scientistfilter_member)->pluck('id')->first();
        $get_my_learning_devlopment_idea = $get_my_learning_devlopment_idea->where('scientist', 'like', '%"' . $user_id . '"%');
    }

    if (Auth::user()->role == 1) {
        $get_learning_devlopment_idea_list = $get_my_learning_devlopment_idea->get();
    } elseif (Auth::user()->role == 5) {
        $get_learning_devlopment_idea_list = $get_my_learning_devlopment_idea->get();
    } elseif (Auth::user()->role == 3) {
        $user_ids = User::where('role', 2)
                        ->where('supervisor_id', Auth::user()->id)
                        ->pluck('id');

        $user_ids = $user_ids->merge([Auth::user()->id]);
        $get_learning_devlopment_idea_list = $get_my_learning_devlopment_idea->whereIn('created_by', $user_ids)->get();
    } elseif (Auth::user()->role == 4 || Auth::user()->role == 6) {
        $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)
                            ->orWhere('id', Auth::id())->pluck('id');
        $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)
                            ->orWhere('id', Auth::id())->pluck('id');

        $get_learning_devlopment_idea_list = $get_my_learning_devlopment_idea->whereIn('created_by', $user_listid)->get();
    } elseif (Auth::user()->role == 2) {
        $get_learning_devlopment_idea_list = $get_my_learning_devlopment_idea->where('created_by', Auth::user()->id)->get();
    } else {
        $user_listid = User::where('status', 1)->pluck('id');
        $getcollavrtivememid = [];

        foreach ($user_listid as $value) {
            $get_pending_learning = $get_my_learning_devlopment_idea->get()->toArray();
            foreach ($get_pending_learning as $getvalue) {
                if (Auth::user()->id == $getvalue['created_by']) {
                    array_push($getcollavrtivememid, [$value => $getvalue['id']]);
                }
            }
        }

        $get_learning_devlopment_idea_list = $get_my_learning_devlopment_idea->whereIn('id', $getcollavrtivememid)->get();
    }
    $scientistCounts = $get_learning_devlopment_idea_list
    ->groupBy('created_by')
    ->map(function ($items, $scientistId) {
        $user = User::find($scientistId);
        return [
            'scientist_name' => $user ? $user->name : 'Unknown',
            'count' => $items->count()
        ];
    })
    ->values(); 

   // dd($get_learning_devlopment_idea_list->toArray(),'________query');
    return Datatables::of($get_learning_devlopment_idea_list)
    ->addColumn('idea_category',function ($get_learning_devlopment_idea_list) {
        return $get_learning_devlopment_idea_list->idea_category;
    })
    ->addColumn('idea_type', function ($get_learning_devlopment_idea_list) {
        return $get_learning_devlopment_idea_list->idea_type;
    })
    
    ->addColumn('insights', function($get_learning_devlopment_idea_list) {
        $ideaInsightIds = json_decode($get_learning_devlopment_idea_list->idea_insight, true);
                if (is_array($ideaInsightIds)) {
            $ideaInsightIds = array_map('intval', $ideaInsightIds);
            $get_insights = LanddInsight::whereIn('id', $ideaInsightIds)->pluck('insights_generated')->toArray();
            //dd($get_insights);
        } else {
            $get_insights = [];
        }
    
        return !empty($get_insights) ? implode(', ', $get_insights) : '-';
    })
    
    
              
       ->addColumn('scientist', function ($get_learning_devlopment_idea_list) {
        $scientistIds = json_decode($get_learning_devlopment_idea_list->scientist, true);
        if (is_array($scientistIds)) {
            $coCreators = User::whereIn('id', $scientistIds)->get();
        } else {
            $coCreators = User::where('id', $scientistIds)->get();
        }
        $scientistNames = $coCreators->pluck('name')->toArray();
        $currentUser = Auth::user()->name;
        if (count($scientistNames) == 1 && $scientistNames[0] == $currentUser) {
            return "-";
        } else {
            $scientistNames = array_diff($scientistNames, [$currentUser]);
            return implode(', ', $scientistNames);
        }
       })

       ->addColumn('idea_txt',function($get_learning_devlopment_idea_list){
           return $get_learning_devlopment_idea_list->idea_txt;
       })
       ->addColumn('scientist_name', function ($get_learning_devlopment_idea_list) {
        $scientist = '';
        $user_id = $get_learning_devlopment_idea_list->created_by;
        $user = User::where('id', $user_id)->first();
        $scientist .= $user->name;
        return $scientist . '';
    })
          ->addColumn('ideas_comments', function ($get_learning_devlopment_idea_list) {
              return $get_learning_devlopment_idea_list->ideas_comments;
          })
       ->addColumn('supervisior_name', function ($get_learning_devlopment_idea_list) {
        $supervisior_name = '';
        $user_name = User::where('id', $get_learning_devlopment_idea_list->created_by)->first();
        $supervisior = User::where('id', $user_name->supervisor_id)->first();
        // if ($supervisior->id == 68) {
        //     $supervisior_name .='-';
        // }else{
            $supervisior_name .= $supervisior->name;
        // }
        return $supervisior_name . '';
    })
    ->addColumn('target_date', function ($get_learning_devlopment_idea_list) {
        return $get_learning_devlopment_idea_list->target_date;
    })
       
        ->addColumn('created_by', function ($get_learning_devlopment_idea_list){
            $user = User::where('id', $get_learning_devlopment_idea_list->created_by)->first();
            return $user->name;
        })
        
        ->addColumn('actions', function ($get_learning_devlopment_idea_list) {
           $action = '';
           $action .= '<button class="btn btn-info btn-icon" onClick="editL_D_idea(' . $get_learning_devlopment_idea_list->id . ');"><i class="fa fa-edit"></i></button>';
           $action .= '<button class="btn btn-danger btn-icon" onClick="deleteL_D_idea(' . $get_learning_devlopment_idea_list->id . ');"><i class="fa fa-trash"></i></button>';
           return $action. '';
        })
        ->with('scientistCounts', $scientistCounts) // Pass the counts to the view
        ->rawColumns(array("ideas_generated", "idea_txt", "idea_type",  "supervisior_name", "target_date", "ideas_comments", "created_by", "actions","created_date", "parent_idea_gen","created_current_date", "scientist_name"))
        ->make(true);
}
   
  
    public function save_learning_development(Request $request)
{
    $data = $request->all();
    //dd($data);
    $userId = Auth::user()->id;

    // Fetch the latest record created by the user
    $lastRecord = LearningDevelopment::where('created_by', $userId)
        ->where('deleted_status', 0)
        ->orderBy('created_date', 'desc')
        ->first();

    if ($lastRecord) {
        $lastCreatedDate = Carbon::parse($lastRecord->created_date);
        $daysSinceLastEntry = Carbon::now()->diffInDays($lastCreatedDate);

        if ($daysSinceLastEntry < 7) {
            $remainingDays = 7 - $daysSinceLastEntry;
            return response()->json([
                'success' => false,
                'errors' => ['weekly_limit' => "You can only create a new entry after 7 days from your last submission. Please wait $remainingDays more days."]
            ]);
        }
    }

    // Validation rules
    $validator = Validator::make($data, [
        'topics_learnt' => 'required',
        'topics_present' => 'required',
        'hours_invested' => 'required|numeric|min:0',
        'vendor_facility_visited' => 'required|numeric|min:0',
        'conferences_attended' => 'required|numeric|min:0',
        'exhibitions_attended' => 'required|numeric|min:0',
        'consumer_retail_visits' => 'required|numeric|min:0',
        'ideas_generated' => 'required|numeric|min:0',
        'weekly_presentation.*' => 'required|file|mimes:pdf,ppt,pptx,doc,docx|max:4096', 
        'date_range_start' => 'required|date',
        'date_range_end' => 'required|date|after_or_equal:date_range_start',

    ]);

    if ($validator->fails()) {
        return response()->json(['success' => false, 'errors' => $validator->errors()]);
    }

    $filePaths = [];

    if ($request->hasFile('weekly_presentation')) {
        $files = $request->file('weekly_presentation');

        foreach ($files as $file) {
            $allowedMimeTypes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation'
            ];

            if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
                return response()->json([
                    'success' => false,
                    'errors' => ['weekly_presentation' => 'Only PDF, DOC, DOCX, PPT, and PPTX files are allowed.']
                ]);
            }

            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('presentations', $fileName, 'public');
            $filePaths[] = $fileName; // Store only filename, not full path
        }
    } 
    // else {
    //     return response()->json([
    //         'success' => false,
    //         'errors' => ['weekly_presentation' => 'At least one presentation file is required.']
    //     ]);
    // }    

    LearningDevelopment::create([
        'topics_learnt' => $data['topics_learnt'],
        'topics_present' => $data['topics_present'],
        'no_of_hours_invested_in_iiy' => $data['hours_invested'],
        'no_of_vendor_facility_visited' => $data['vendor_facility_visited'],
        'no_of_conference_attended' => $data['conferences_attended'],
        'no_of_exhibitions_attended' => $data['exhibitions_attended'],
        'no_of_consumer_and_retail_visits' => $data['consumer_retail_visits'],
        'no_of_ideas_generated_through_iiy' => $data['ideas_generated'],
        'upload_the_presentation_weekly' => json_encode($filePaths), 
        'date_range_start' => $data['date_range_start'],
        'date_range_end' => $data['date_range_end'],
        'created_by' => $userId,
        'created_date' => Carbon::now()->format('Y-m-d'),
        'month' => Carbon::now()->format('Y-m') . '-01',
    ]);

    return response()->json(['success' => true]);
}

    
    
    
    
    // public function get_topics_learnt_list(Request $request){
    //     $topics_learnt = LearningDevelopment::select('id','topics_learnt','source_learning','area_of_development','other_field','project_name')->where('created_by', Auth::user()->id)->where('deleted_status', 0)->get();
    //     return response()->json(['topics_learnt' => $topics_learnt]);
    // }
    

    public function save_learning_devlopment_insight(Request $request)
    {     
        //dd($request->all());  
        $cur_date = Carbon::now()->format('Y-m-d');
        $data = $request->all();
       //dd($data);
    
        $LanddInsight = LanddInsight::create([
            'insights_generated' => $data['insight_generated'],
            'insights_category' => $data['insight_category'],
            'scientist' => $data['team_member'],
            'remark' => $data['insight_remarks'],
            'created_date' => Carbon::now()->format('Y-m').'-01',
            'created_current_date' => $cur_date,
            'finance_status' => 1,
            'created_by' => Auth::user()->id,
             
        ]);
        //dd($LanddInsight);

        // Return success response
        return response()->json(['success' => true]); 
    }
    

    public function updateinsight(Request $req)
    {

        DB::table('tbl_landd_insights')->where('id', $req->insightid)->update([
            'insights_generated' => $req->insight_generated,
            'scientist' => $req->scientist,

            'insights_category' => $req->insight_category,
            'remark' => $req->insight_remarks
        ]);
        return response(1);
    }
    public function scientist_for_tl(Request $req)
    {
        //dd($req->all());
        if(($req->month_bussiness =='2023-03')||($req->month_bussiness =='2023-02')){
            $table ='users_old' ;
        }else{
            $table = 'users';
        }
        $reporting_scientist_data = DB::table($table)->where('name', $req->scientist)->first();
        $reporting_scientist = DB::table($table)->where('supervisor_id', $reporting_scientist_data->id)->where('status', 1)->select('id','name','role','supervisor_id')->get();
        $scientist_cl_tl = DB::table($table)->where('id', $reporting_scientist_data->supervisor_id)->where('status', 1)->select('id','name','role','supervisor_id')->get();
        $scientist_cl_id = DB::table($table)->where('id', $reporting_scientist_data->supervisor_id)->where('status', 1)->first();
        if($scientist_cl_id->role == '3'){
            $scientist_cl = DB::table($table)->where('id', $scientist_cl_id->supervisor_id)->where('status', 1)->select('id','name','role','supervisor_id')->get();
        }else{
            $scientist_cl = DB::table($table)->where('id', $reporting_scientist_data->supervisor_id)->where('status', 1)->select('id','name','role','supervisor_id')->get();
        }
        $user_id =Auth::user()->id; 
        $data = [
            'reporting_scientist' => $reporting_scientist,
            'scientist_cl_tl' => $scientist_cl_tl,
            'scientist_cl' => $scientist_cl,
            'user_id' => $user_id,
        ];
        return $data;
    }
    public function get_learning_devlopment_list(Request $req)
    {       
        //dd(Auth::user()->role);     
        //dd($req->scientistfilter);

        //dd($req->scientistfilter_tl);
        $get_my_learning_development = LearningDevelopment::query()->where('deleted_status','0');
        // dd($get_my_learning_development->toSql(),'___sql');
        if( $req->scientistfilter_cl != 'all' && $req->scientistfilter_cl != ''){
            $cl_id = User::where('name', $req->scientistfilter_cl)->pluck('id')->first();
            if ($cl_id) {
                $get_my_learning_development = $get_my_learning_development->whereIn('created_by', [$cl_id]);
            }
        }
        
        if( $req->scientistfilter_tl != 'all' && $req->scientistfilter_tl != ''){
            $tl_id = User::where('name', $req->scientistfilter_tl)->pluck('id')->first();
            $get_my_learning_development = $get_my_learning_development->where('created_by', $tl_id);
        }
        
        if( $req->scientistfilter_member != 'all' && $req->scientistfilter_member != ''){
            $user_id = User::where('name', $req->scientistfilter_member)->pluck('id')->first();
            $get_my_learning_development = $get_my_learning_development->where('created_by', $user_id);
        }
        // } else {
        //     if(in_array(Auth::user()->role, [4, 6,3,5])){
        //         $supervisor_id = Auth::id();
        //         $team_member_ids = User::where('supervisor_id', $supervisor_id)->pluck('id');
        //         $get_my_learning_development = $get_my_learning_development->whereIn('created_by', $team_member_ids);
        //     } else {
        //         $get_my_learning_development = $get_my_learning_development->where('created_by', Auth::id());
        //     }
        


        //     $tl_id = User::where('supervisor_id', $cl_id)->pluck('id')->toArray();
        //     $tm_id = User::whereIn('supervisor_id', $tl_id)->pluck('id')->toArray();
        //     $user_role = User::where('name', $req->scientistfilter_cl)->pluck('role')->first();
        //     if ($user_role == 5) {
        //         $mapped_id = array_merge($tl_id, $tm_id);
        //         $unique_user_id = array_unique($mapped_id);
        //         $user_id = User::whereIn('supervisor_id', $unique_user_id)->pluck('id')->toArray();
        //     } else {
        //         $user_id = array_merge($tl_id, $tm_id);
        //     }
        //     $user_id[] = $cl_id;
        //     $get_my_learning_development = $get_my_learning_development->whereIn('created_by', $user_id);

        // }
        
        // if( $req->scientistfilter_member != 'all' && $req->scientistfilter_member != ''){
        //     $user_id = User::where('name', $req->scientistfilter_member)->pluck('id')->first();
        //     $get_my_learning_development = $get_my_learning_development->where('created_by', $user_id);


        
        if($req->monthgoal != ''){
        $get_my_learning_development = $get_my_learning_development->where('month', $req->monthgoal."-01");
        }
        else
        {
            $get_my_learning_development = $get_my_learning_development->where('month', date('Y-m')."-01");
        }
    
        if (Auth::user()->role == 1) {
            $get_learning_devlopment_list =  $get_my_learning_development->get();
        }else if (Auth::user()->role == 5) {
            $get_learning_devlopment_list = $get_my_learning_development->get();
        }
        
        else if (Auth::user()->role == 4) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $get_learning_devlopment_list =  $get_my_learning_development->whereIn('created_by', $user_listid)->get();
        } else if (Auth::user()->role == 6) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $get_learning_devlopment_list =  $get_my_learning_development->whereIn('created_by', $user_listid)->get();
        } else  if (Auth::user()->role == 3) {
            $user_ids = User::where('status', 1)
                        ->where('role', 2)
                        ->where('supervisor_id', Auth::user()->id)
                        ->pluck('id');
        
        // Add the current user's data as well
        $user_ids = $user_ids->merge([Auth::user()->id]);
            $get_learning_devlopment_list =  $get_my_learning_development->WhereIn('created_by', $user_ids)->get(); 
        } 
        else if(Auth::user()->role == 2){
                $get_learning_devlopment_list =  $get_my_learning_development->where('created_by', Auth::user()->id)
                ->get();
        }else{
            $get_learning_devlopment_list =  $get_my_learning_development->where('created_by', Auth::user()->id)
            ->get();
        }
        return Datatables::of($get_learning_devlopment_list)
            ->addColumn('topic_learnt', function ($get_learning_devlopment_list) {
                return $get_learning_devlopment_list->topics_learnt;
            })
            ->addColumn('topic_presented', function ($get_learning_devlopment_list) {
                return $get_learning_devlopment_list->topics_present;
            })
            ->addColumn('hours_invested', function ($get_learning_devlopment_list) {
                return $get_learning_devlopment_list->no_of_hours_invested_in_iiy;
            })
            ->addColumn('vendor_facility_visited', function ($get_learning_devlopment_list) {
                return $get_learning_devlopment_list->no_of_vendor_facility_visited;
            })
            ->addColumn('conferences_attended', function ($get_learning_devlopment_list) {
                return $get_learning_devlopment_list->no_of_conference_attended;
            })
            ->addColumn('exhibitions_attended', function ($get_learning_devlopment_list) {
                return $get_learning_devlopment_list->no_of_exhibitions_attended;
            })
            ->addColumn('consumer_retail_visits', function ($get_learning_devlopment_list) {
                return $get_learning_devlopment_list->no_of_consumer_and_retail_visits;
            })
            ->addColumn('ideas_generated', function ($get_learning_devlopment_list) {
                return $get_learning_devlopment_list->no_of_ideas_generated_through_iiy;
            })
            ->addColumn('weekly_presentation', function ($get_learning_devlopment_list) {
                return pathinfo($get_learning_devlopment_list->upload_the_presentation_weekly, PATHINFO_BASENAME);
            })
            ->addColumn('date_range', function ($get_learning_devlopment_list) {
                return $get_learning_devlopment_list->date_range_start . ' to ' . $get_learning_devlopment_list->date_range_end;
            })
            ->addColumn('scientist', function ($get_learning_devlopment_list) {
                $user = User::find($get_learning_devlopment_list->created_by);
                return $user?->name ?? 'N/A';
            })
            
            
            ->addColumn('created_by', function ($get_learning_devlopment_list) {
                $created_by = '';
                $user = $get_learning_devlopment_list->created_by;
                $user_name = DB::table('users')->where('id', $user)->first();
                $created_by .= $user_name->name;
                return $created_by . '';
            })
            ->addColumn('actions', function ($get_learning_devlopment_list) {                                  
                return '<td>
                            <button class="btn btn-success btn-icon edit-iiy-btn" 
                                data-id="' . e($get_learning_devlopment_list->id) . '" 
                                data-bs-toggle="modal" 
                                data-bs-target="#edit_learning_development_modal">
                                <i class="fa fa-edit"></i>
                            </button>
                        </td>';
            })
            
            
            // ->addColumn('rating', function ($get_learning_devlopment_list) {
            //     $rating = '';
            //     $rating .= '<div class="rating">';
            //     for ($i = 1; $i <= 5; $i++) {
            //         if ($i <= $get_learning_devlopment_list->current_skill_level) {
            //             $rating .= '<span class="fa fa-star checked"></span>';
            //         } else {
            //             $rating .= '<span class="fa fa-star"></span>';
            //         }
            //     }
            //     $rating .= '</div>';
            //     return $rating;
            // })
            
            
            ->rawColumns(array('topic_learnt','topic_presented','' ,"remarks","current_date" ,"actions","scientist",))
            ->make(true);
    }

    public function get_learning_development_evaluation_list(Request $req)
    {
        //dd($req->all());
        $data = LearningDevelopment::all();
        $currentUser = Auth::user();
        $userId = $currentUser->id;   
        $teamMemberIds = User::where('supervisor_id', $userId)->pluck('id')->toArray();  
        $subordinatesIds = User::whereIn('supervisor_id', $teamMemberIds)->pluck('id')->toArray();
 
        $currentUserRole = $currentUser->role; 

        if ($currentUserRole == '5') { 
            $allUserIds = $teamMemberIds;
        } elseif ($currentUserRole == '3') {
            $allUserIds = $teamMemberIds; 
        } else { 
            $allUserIds = array_merge($teamMemberIds, $subordinatesIds);
        }
        
        $get_evaluation_list = LearningDevelopment::query()
            ->where('deleted_status', '0')
            ->whereIn('created_by', $allUserIds);
    
            if ($req->scientistfilter_cl != 'all' && $req->scientistfilter_cl != '') {
                // // Handle Cell Lead filtering
                // $cl_id = User::where('name', $req->scientistfilter_cl)->pluck('id')->first();
                // $tl_ids = User::where('supervisor_id', $cl_id)->pluck('id')->toArray();
                // $tm_ids = User::whereIn('supervisor_id', $tl_ids)->pluck('id')->toArray();
                // $user_role = User::where('id', $cl_id)->value('role');
            
                // if ($user_role == 5) {
                //     // If CL is actually a TL, treat as TL (fallback)
                //     $user_id = array_merge($tl_ids, $tm_ids);
                // } else {
                //     // CL > TL > TM
                //     $user_id = array_merge($tl_ids, $tm_ids);
                //     $user_id[] = $cl_id;
                // }
            
                // $get_evaluation_list = $get_evaluation_list->whereIn('created_by', $user_id);
                $cl_id = User::where('name', $req->scientistfilter_cl)->pluck('id')->first();
                if($cl_id){
                    $get_evaluation_list = $get_evaluation_list->where('created_by', $cl_id);
                }
            }
            
            if ($req->scientistfilter_tl != 'all' && $req->scientistfilter_tl != '') {
                $tl_id = User::where('name', $req->scientistfilter_tl)->pluck('id')->first();
                // $tm_ids = User::where('supervisor_id', $tl_id)->pluck('id')->toArray();
                // $user_id = array_merge([$tl_id], $tm_ids);
            
                $get_evaluation_list = $get_evaluation_list->whereIn('created_by', $user_id);
            }
            
            // if ($req->scientistfilter_tm != 'all' && $req->scientistfilter_tm != '') {
            //     $tm_id = User::where('name', $req->scientistfilter_tm)->pluck('id')->first();
            //     $get_evaluation_list = $get_evaluation_list->where('created_by', $tm_id);
            // }
            
            if ($req->scientistfilter_member != 'all' && $req->scientistfilter_member != '') {
                $user_id = User::where('name', $req->scientistfilter_member)->pluck('id')->first();
                $get_evaluation_list = $get_evaluation_list->where('created_by', $user_id);
            } else {
                if(in_array(Auth::user()->role, [4, 6,3,5])){
                    $supervisor_id = Auth::id();
                    $team_member_ids = User::where('supervisor_id', $supervisor_id)->pluck('id');
                    $get_evaluation_list = $get_evaluation_list->whereIn('created_by', $team_member_ids);
                } else {
                    $get_evaluation_list = $get_evaluation_list->where('created_by', Auth::id());
                }
            }
            
    
        if ($req->monthvalue != '') {
            $get_evaluation_list = $get_evaluation_list->where('month', $req->monthvalue . "-01");
        } else {
            $get_evaluation_list = $get_evaluation_list->where('month', date('Y-m') . "-01");
        }
    
        $get_learning_development_list = $get_evaluation_list->get();
    
        return DataTables::of($get_learning_development_list)
            ->addColumn('topic_learnt', function ($row) {
                return $row->topics_learnt;
            })
            ->addColumn('topic_presented', function ($row) {
                return $row->topics_present;
            })
           ->addColumn('team_member', function ($row) {
               $user = DB::table('users')->where('id', $row->created_by)->first();
               return $user ? $user->name : 'Unknown';
           })
            ->addColumn('rating', function ($row) {
                return $row->rating;
            })
           
            ->addColumn('weekly_presentation', function ($row) {
                if (!empty($row->upload_the_presentation_weekly)) {
                    $files = json_decode($row->upload_the_presentation_weekly, true);
                     if (is_array($files) && count($files) > 0) {
                        return basename($files[0]);
                    }
                }
                return 'No file uploaded';
            })
            ->addColumn('feedback', function ($row) {
                return $row->feedback;
            })
            
            ->addColumn('created_by', function ($row) {
                $user = DB::table('users')->where('id', $row->created_by)->first();
                return $user ? $user->name : 'Unknown';
            })
            ->addColumn('actions', function ($row) {
                return '<button class="btn btn-info btn-icon view-iiy-btn" 
                               data-id="' . $row->id . '" 
                               data-bs-toggle="modal" 
                               data-bs-target="#view_learning_development_modal">
                               <i class="fa-solid fa-star-half-stroke"></i>
                       </button>';
            })
            ->rawColumns(['actions'])
            ->make(true);
    }
    

    // public function edit_learning_development1(Request $request) {
    //     $l_dlist = DB::table('tbl_learning_devlopment')->where('id', $request->id)->first();
    
    //     return response()->json([
    //         'topics_learnt' => $l_dlist->topics_learnt ?? '',
    //         'topics_present' => $l_dlist->topics_present ?? '',
    //         'hours_invested' => $l_dlist->no_of_hours_invested_in_iiy ?? '',
    //         'vendor_facility_visited' => $l_dlist->no_of_vendor_facility_visited ?? '',
    //         'conferences_attended' => $l_dlist->no_of_conference_attended ?? '',
    //         'exhibitions_attended' => $l_dlist->no_of_exhibitions_attended ?? '',
    //         'consumer_retail_visits' => $l_dlist->no_of_consumer_and_retail_visits ?? '',
    //         'ideas_generated' => $l_dlist->no_of_ideas_generated_through_iiy ?? '',
    //         'uploaded_files' => collect(json_decode($l_dlist->upload_the_presentation_weekly))
    //         ->map(function ($file) {
    //             return asset('storage/' . $file);
    //         })     
    //        ]);
    // }


    public function getDetails(Request $request)
    {
        $id = $request->id;
        $learningDevelopment = LearningDevelopment::find($id);
        
        if (!$learningDevelopment) {
            return response()->json(['error' => 'Record not found'], 404);
        }
    
        $rating = $learningDevelopment->rating ?? "0/5";
        if (!str_contains($rating, "/")) {
            $rating = "0/5"; 
        }
    
        return response()->json([
            'title_id' => $id,
            'view_title' => $learningDevelopment->topics_learnt ?? "",
            'view_topics_present' => $learningDevelopment->topics_present ?? "",
            'view_rating' => $rating,
            'uploaded_files_list' => collect(json_decode($learningDevelopment->upload_the_presentation_weekly))
            ->map(function ($file) {
                return asset('storage/' . $file);
            }),
            
            'view_feedback' => $learningDevelopment->feedback ?? ""
        ], 200, [], JSON_UNESCAPED_SLASHES);
    }
     
    
    
    
    


    public function delete_lnd(Request $req)
    {
        $addform_status= DB::table('tbl_learning_devlopment')->where('id', $req->id)->update([
             "deleted_status" => 1,
         ]);
        if($addform_status){
            $result = array(
                "response" => "success",
            );
            echo json_encode($result);
        }
        else{
            $result = array(
                "response" => "failed",
            );
            echo json_encode($result);
        }
        //return response(1);
    }

    public function get_learning_devlopment_insight(Request $req)
{
    // Initial query setup
    $get_my_insights = LanddInsight::query()->where('deleted_status', '0');

    // Apply filters based on the request (CL, TL, and Member filters)
    if ($req->scientistfilter_cl != 'all' && $req->scientistfilter_cl != '') {
        $cl_id = User::where('name', $req->scientistfilter_cl)->pluck('id')->first();
        
        if ($cl_id) {
            $get_my_insights = $get_my_insights->whereIn('scientist', [$cl_id]);
        }
    }
    
    if ($req->scientistfilter_tl != 'all' && $req->scientistfilter_tl != '') {
        $tl_id = User::where('name', $req->scientistfilter_tl)->pluck('id')->first();
        $get_my_insights = $get_my_insights->where('scientist', $tl_id);
    }

    if ($req->scientistfilter_member != 'all' && $req->scientistfilter_member != '') {
        // Filter by specific team member
        $user_id = User::where('name', $req->scientistfilter_member)->pluck('id')->first();
        $get_my_insights = $get_my_insights->where('scientist', $user_id);
    }

    // Apply month filter if provided
    if ($req->month_insight != '') {
        $get_my_insights = $get_my_insights->where('created_date', $req->month_insight . "-01");
    }

    // Handling different roles
    if (Auth::user()->role == 1) {
        // Admin: Show all insights
        $get_learning_devlopment_list = $get_my_insights->get();
    } 
    else if (Auth::user()->role == 5) {
        // Role 5: Show all insights
        $get_learning_devlopment_list = $get_my_insights->get();
    }
    else if (Auth::user()->role == 3) {
        // Role 3: Show only Role 2 data using supervisor_id and current user data
        // Get Role 2 users under the current user's supervision
        $user_ids = User::where('status', 1)
                        ->where('role', 2)
                        ->where('supervisor_id', Auth::user()->id)
                        ->pluck('id');
        
        // Add the current user's data as well
        $user_ids = $user_ids->merge([Auth::user()->id]); // Include the current user ID
        
        $get_learning_devlopment_list = $get_my_insights->whereIn('scientist', $user_ids)->get();
    } 
    else if (Auth::user()->role == 4 || Auth::user()->role == 6) {
        // Role 4 and Role 6: Show insights for direct and indirect reports
        $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
        $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
        $get_learning_devlopment_list = $get_my_insights->whereIn('scientist', $user_listid)->get();
    }
    else if (Auth::user()->role == 2) {
        // Role 2: Show only their own insights
        $get_learning_devlopment_list = $get_my_insights->where('created_by', Auth::user()->id)->get();
    } else {
        // Default: Show own data if no other role is matched
        $get_learning_devlopment_list = $get_my_insights->where('scientist', Auth::user()->id)->get();
    }
        $scientistCounts = $get_learning_devlopment_list
        ->groupBy('scientist')
        ->map(function ($items, $scientistId) {
            $user = User::find($scientistId);
            return [
                'scientist_name' => $user ? $user->name : 'Unknown',
                'count' => $items->count()
            ];
        })
        ->values(); 
        
       // dd($get_learning_devlopment_list->toArray(),'___query');
        return Datatables::of($get_learning_devlopment_list)

            ->addColumn('insights_generated', function ($get_learning_devlopment_list) {
                return $get_learning_devlopment_list->insights_generated;
            })
            ->addColumn('insights_category', function ($get_learning_devlopment_list) {
                $insights_category = '';
                
                $category = DB::table('tbl_insight_category')->where('id', $get_learning_devlopment_list->insights_category)->first();
                
                if ($category) {
                    $insights_category .= $category->name; 
                }
            
                return $insights_category;
            })
            
            ->addColumn('scientist', function ($get_learning_devlopment_list) {
                $scientist = '';
                $user_id = $get_learning_devlopment_list->scientist;
                $user = User::where('id', $user_id)->first();
                $scientist .= $user->name;
                return $scientist . '';
            })
            ->addColumn('remarks', function ($get_learning_devlopment_list) {
                return $get_learning_devlopment_list->remark;
            })
            ->addColumn('current_date', function ($get_learning_devlopment_list) {
                return date('d-m-Y',strtotime($get_learning_devlopment_list->created_current_date));
            })
            ->addColumn('created_by', function ($get_learning_devlopment_list) {
                $created_by = '';
                $user = $get_learning_devlopment_list->created_by;
                $user_name = DB::table('users')->where('id', $user)->first();
                if ($user_name) {
                    $created_by .= $user_name->name;
                } else {
                    $created_by .= 'Unknown User'; // Or any other default value
                }
            
                return $created_by . '';
            })
            ->addColumn('action', function ($get_learning_devlopment_list) {
                $action = '';
                $action .= '<td>
                    <button class="btn btn-success btn-icon" 
                        onclick="editL_D_insight(' . $get_learning_devlopment_list->id . ')" 
                        data-bs-toggle="modal" 
                        data-bs-target="#edit_insights_modal">
                        <i class="fa fa-edit"></i>
                    </button>
                </td>&nbsp;';
                $action .= '<button class="btn btn-danger btn-icon" onClick="deleteL_D_insight(' . $get_learning_devlopment_list->id . ');"><i class="fa fa-trash"></i></button>';
                return $action . '';
            })
            ->with('scientistCounts', $scientistCounts) // Pass the counts to the view
            ->rawColumns(array("insights_generated", "scientist", "insights_category", "current_date","remark", "action"))
            ->make(true);
           
            
    }

    public function deleteL_D_insight(Request $req)
    {
         $addform_status= DB::table('tbl_landd_insights')->where('id', $req->id)->update([
             "deleted_status" => 1,
         ]);
        if($addform_status){
            $result = array(
                "response" => "success",
            );
            echo json_encode($result);
        }
        else{
            $result = array(
                "response" => "failed",
            );
            echo json_encode($result);
        }
        //return response(1);
    }

    public function deleteL_D_idea(Request $req)
    {
         $addform_status= DB::table('tbl_learning_devlopment_idea')->where('id', $req->id)->update([
             "deleted_status" => 1,
         ]);
        if($addform_status){
            $result = array(
                "response" => "success",
            );
            echo json_encode($result);
        }
        else{
            $result = array(
                "response" => "failed",
            );
            echo json_encode($result);
        }
        //return response(1);
    }

    public function create_collabrative(Request $req)
    {
        DB::table('tbl_collabrative')->insert(['name' => $req->name]);
        return response(1);
    }

    public function get_ipr_list(Request $req)
    {
        $user_id = Auth::user()->id;
        $get_ipr_list = DB::table('tbl_ipr')
            //->where('user', $user_id)
            ->get();
        return Datatables::of($get_ipr_list)
            ->addColumn('id', function ($get_ipr_list) {
                $id = '';
                $id .= $get_ipr_list->id;
                return $id . '';
            })
            ->addColumn('name', function ($get_ipr_list) {
                $name = '';
                $name .= $get_ipr_list->name;
                return $name . '';
            })
            ->rawColumns(array("id", "name"))
            ->make(true);
    }
    public function get_collabrative_list(Request $req)
    {
        $user_id = Auth::user()->id;
        $get_collabrative_list = DB::table('tbl_collabrative')
            //->where('user', $user_id)
            ->get();
        return Datatables::of($get_collabrative_list)
            ->addColumn('id', function ($get_collabrative_list) {
                $id = '';
                $id .= $get_collabrative_list->id;
                return $id . '';
            })
            ->addColumn('name', function ($get_collabrative_list) {
                $name = '';
                $name .= $get_collabrative_list->name;
                return $name . '';
            })
            ->rawColumns(array("id", "name"))
            ->make(true);
    }
    public function save_productivity_outsourcing(Request $req)
    {
        // dd($req->all());
       if($req->created_date==null){
            DB::table('tbl_productivity_outsourcing')->insert([
                'parameter' => $req->parameter_part1,
                'number' => $req->number_part1,
                'scientist'   => $req->scientist,
                'created_date' => date('Y-m')."-01",
                'created_current_date' =>date('Y-m-d'),
            ]);
            DB::table('tbl_productivity_outsourcing')->insert([
                'parameter' => $req->parameter_part2,
                'number' => $req->number_part2,
                'scientist'   => $req->scientist,
                'created_date' => date('Y-m')."-01",
                'created_current_date' =>date('Y-m-d'),
            ]);
            // DB::table('tbl_productivity_outsourcing')->createOrUpdate(['parameter' => $req->parameter, 'scientist' => $req->scientist, 'number' => $req->number, 'outsourced' => $req->outsourced]);
            return response(1);
       }else{
            DB::table('tbl_productivity_outsourcing')->insert([
                'parameter' => $req->parameter_part1,
                'number' => $req->number_part1,
                'scientist'   => $req->scientist,
                'created_date' => date('Y-m')."-01",
                'created_current_date' =>$req->created_date,
            ]);
            DB::table('tbl_productivity_outsourcing')->insert([
                'parameter' => $req->parameter_part2,
                'number' => $req->number_part2,
                'scientist'   => $req->scientist,
                'created_date' => date('Y-m')."-01",
                'created_current_date' =>$req->created_date,
            ]);
            // DB::table('tbl_productivity_outsourcing')->createOrUpdate(['parameter' => $req->parameter, 'scientist' => $req->scientist, 'number' => $req->number, 'outsourced' => $req->outsourced]);
            return response(1);
       }
        
    }
    public function delete_productivity_outsourcing(Request $req)
    {
         $addform_status= DB::table('tbl_productivity_outsourcing')->where('id', $req->id)->update([
             "deleted_status" => 1,
         ]);
        if($addform_status){
            $result = array(
                "response" => "success",
            );
            echo json_encode($result);
        }
        else{
            $result = array(
                "response" => "failed",
            );
            echo json_encode($result);
        }
        //return response(1);
    }
    public function get_productivity_outsourcing_list_overall(Request $req)
    {
        $get_my_productivity_outsourcing = DB::table('tbl_productivity_outsourcing')->where('deleted_status','0');
            if($req->monthgoal_Assays != ''){
                // dd(1);
            $get_my_productivity_outsourcing = $get_my_productivity_outsourcing->where('created_date', $req->monthgoal_Assays."-01");
            }
            else
            {
                // dd(2);
                //$get_my_productivity_outsourcing = $get_my_productivity_outsourcing->where('created_date', date('Y-m')."-01");
                $get_my_productivity_outsourcing = $get_my_productivity_outsourcing;
            }
             if (Auth::user()->role == 1) {
                $myteams = DB::table('users')->whereNotIn('division_id', [1, 5])->where('supervisor_id', Auth::id())->orWhere('id', Auth::id())->pluck('id');
                $get_productivity_outsourcing_list = DB::table('tbl_productivity_outsourcing')
                    ->select('parameter', 'scientist', 'created_date', DB::raw('SUM(number) AS newnumber'))
                    //->where('created_date', date('Y-m') . "-01")
                    ->where('deleted_status', '0')
                    ->whereIn('parameter', ['No.of Formulation in CKPL', 'No.of Formulation Outsourced to HEPL'])

                    ->groupBy('parameter')
                    ->get();
            } else if (Auth::user()->role == 5 || Auth::user()->role == 4) {
                $myteams = DB::table('users')->whereNotIn('division_id', [1, 5])->where('supervisor_id', Auth::id())->orWhere('id', Auth::id())->pluck('id');
                $get_productivity_outsourcing_list = DB::table('tbl_productivity_outsourcing')
                    ->select('parameter', 'scientist', 'created_date', DB::raw('SUM(number) AS newnumber'))
                    //->where('created_date', date('Y-m') . "-01")
                    ->where('created_date',$req->monthgoal_Assays."-01")
                    ->where('deleted_status', '0')
                    ->whereIn('parameter', ['No.of Formulation in CKPL', 'No.of Formulation Outsourced to HEPL'])
                    ->groupBy('parameter')
                    ->get();
                    // dd($get_productivity_outsourcing_list);
            } else  {
                // dd(1);
                $myteams = DB::table('users')->whereNotIn('division_id', [1, 5])->where('supervisor_id', Auth::id())->orWhere('id', Auth::id())->pluck('id');
                $get_productivity_outsourcing_list = DB::table('tbl_productivity_outsourcing')
                    ->select('parameter', 'scientist', 'created_date', DB::raw('SUM(number) AS newnumber'))
                    //->where('created_date', date('Y-m') . "-01")
                    ->where('deleted_status', '0')
                    ->where('created_date',$req->monthgoal_Assays."-01")
                    ->whereIn('parameter', ['No.of Formulation in CKPL', 'No.of Formulation Outsourced to HEPL'])
                    ->whereIn('scientist', $myteams)
                    ->groupBy('parameter')
                    ->get();
                    // dd($get_productivity_outsourcing_list);
            }
        return Datatables::of($get_productivity_outsourcing_list)
            ->addColumn('parameter', function ($get_productivity_outsourcing_list) {
                $parameter = '';
                $parameter .= $get_productivity_outsourcing_list->parameter;
                return $parameter . '';
            })
            // ->addColumn('scientist', function ($get_productivity_outsourcing_list) {
            //     $scientist = '';
            //     $user = $get_productivity_outsourcing_list->scientist;
            //     $user_name = DB::table('users')->where('id', $user)->where('status', 1)->first();
            //     $scientist .= $user_name->name;
            //     return $scientist . '';
            // })
            ->addColumn('number', function ($get_productivity_outsourcing_list) {
                $number = '';
                $number .= $get_productivity_outsourcing_list->newnumber;
                // print_r($number);
                return $number . '';
            })
            ->addColumn('outsourced', function ($get_productivity_outsourcing_list) {
                $user_list = DB::table('users')->whereNotIn('division_id', [1, 5])->pluck('id');
                $outsourced='';
                $outsourcedpercentge = DB::table('tbl_productivity_outsourcing')->whereIn('scientist',$user_list)->whereIn('parameter', ['No.of Formulation in CKPL'])
                ->where('deleted_status','0')->where('created_date',$get_productivity_outsourcing_list->created_date)->sum('number');
                $heploutsourced = DB::table('tbl_productivity_outsourcing')->whereIn('scientist',$user_list)->where('deleted_status','0')->where('parameter', 'No.of Formulation Outsourced to HEPL')->where('created_date',$get_productivity_outsourcing_list->created_date)->sum('number');
                // dd($outsourcedpercentge,$heploutsourced);
                 $outsourced .= '';
                $outsourcedtotal=$outsourcedpercentge + $heploutsourced;
                // dd($outsourcedpercentge,$heploutsourced);
                $outsourced .= sprintf('%.2f', ($heploutsourced) / ($outsourcedtotal) * 100);
                return $outsourced.'';
            })
            ->rawColumns(array("parameter",  "number", "outsourced"))
            ->make(true);
    }
    public function productivity_outsourcing_list_overall_Assays(Request $req)
    {
        $get_my_productivity_outsourcing = DB::table('tbl_productivity_outsourcing')->where('deleted_status','0');
            if($req->monthgoal_Assays_overall != ''){
                // dd(1);
            $get_my_productivity_outsourcing = $get_my_productivity_outsourcing->where('created_date', $req->monthgoal_Assays_overall."-01");
            }
            else
            {
                // dd(2);
                //$get_my_productivity_outsourcing = $get_my_productivity_outsourcing->where('created_date', date('Y-m')."-01");
                $get_my_productivity_outsourcing = $get_my_productivity_outsourcing;
            }
        if (Auth::user()->role == 1) {
            $myteams = DB::table('users')->whereNotIn('division_id', [3, 19])->where('supervisor_id', Auth::id())->orWhere('id', Auth::id())->pluck('id');
            $get_productivity_outsourcing_list = DB::table('tbl_productivity_outsourcing')
            ->select('parameter', 'scientist', 'created_date', DB::raw('SUM(number) AS newnumber'))
            //->where('created_date', date('Y-m') . "-01")
            ->where('deleted_status', '0')
            ->whereIn('parameter', ['No. of Assays done in HEPL', 'No. of Assays done in CKPL'])
            ->groupBy('parameter')
            ->get();
        } else if (Auth::user()->role == 5 || Auth::user()->role == 4) {
        $myteams = DB::table('users')->whereNotIn('division_id', [3, 19])->pluck('id');
        // dd($myteams);
        $get_productivity_outsourcing_list = DB::table('tbl_productivity_outsourcing')
            ->select('parameter', 'scientist', 'created_date', DB::raw('SUM(number) AS newnumber'))
            //->where('created_date', date('Y-m') . "-01")
            ->where('created_date',$req->monthgoal_Assays_overall."-01")
            ->where('deleted_status', '0')
            ->whereIn('parameter', ['No. of Assays done in HEPL', 'No. of Assays done in CKPL'])
            ->whereIn('scientist',$myteams)
            ->groupBy('parameter')
            ->get();
        } else if(Auth::user()->role == 6) {
            $user_listid1 = User::where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $myteams = User::whereIn('id', $user_listid)->whereNotIn('division_id', [3, 19])->pluck('id');

            $get_productivity_outsourcing_list = DB::table('tbl_productivity_outsourcing')
            ->select('parameter', 'scientist', 'created_date', DB::raw('SUM(number) AS newnumber'))
            ->where('created_date',$req->monthgoal_Assays_overall."-01")
            ->where('deleted_status', '0')
            ->whereIn('parameter', ['No. of Assays done in HEPL', 'No. of Assays done in CKPL'])
            ->whereIn('scientist',$myteams)
            ->groupBy('parameter')
            ->get();

        } else if(Auth::user()->role == 3) {
            $user_listid = User::where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $myteams = User::whereIn('id', $user_listid)->whereNotIn('division_id', [3, 19])->pluck('id');

            $get_productivity_outsourcing_list = DB::table('tbl_productivity_outsourcing')
            ->select('parameter', 'scientist', 'created_date', DB::raw('SUM(number) AS newnumber'))
            ->where('created_date',$req->monthgoal_Assays_overall."-01")
            ->where('deleted_status', '0')
            ->whereIn('parameter', ['No. of Assays done in HEPL', 'No. of Assays done in CKPL'])
            ->whereIn('scientist',$myteams)
            ->groupBy('parameter')
            ->get();
        } else  {
        $myteams = DB::table('users')->whereNotIn('division_id', [3, 19])->where('supervisor_id', Auth::id())->orWhere('id', Auth::id())->pluck('id');
        $get_productivity_outsourcing_list = DB::table('tbl_productivity_outsourcing')
            ->select('parameter', 'scientist', 'created_date', DB::raw('SUM(number) AS newnumber'))
            ->where('created_date',$req->monthgoal_Assays_overall."-01")
            ->where('deleted_status', '0')
            ->whereIn('parameter', ['No. of Assays done in HEPL', 'No. of Assays done in CKPL'])
            ->whereIn('scientist',$myteams)
            ->groupBy('parameter')
            ->get();
            // dd($get_productivity_outsourcing_list );
        }

        return Datatables::of($get_productivity_outsourcing_list)
            ->addColumn('parameter', function ($get_productivity_outsourcing_list) {
                $parameter = '';
                $parameter .= $get_productivity_outsourcing_list->parameter;
                return $parameter . '';
            })

            ->addColumn('number', function ($get_productivity_outsourcing_list) {
                $number = '';
                $number .= $get_productivity_outsourcing_list->newnumber;
                return $number . '';
            })
            ->addColumn('outsourced', function ($get_productivity_outsourcing_list) {
                $user_list = DB::table('users')->whereNotIn('division_id', [3, 19])->pluck('id');
                $outsourced='';
                $outsourcedpercentge = DB::table('tbl_productivity_outsourcing')->whereIn('scientist',$user_list)->whereIn('parameter', ['No. of Assays done in CKPL'])
                ->where('deleted_status','0')->where('created_date',$get_productivity_outsourcing_list->created_date)->sum('number');
                $heploutsourced = DB::table('tbl_productivity_outsourcing')->whereIn('scientist',$user_list)->where('deleted_status','0')->where('parameter', 'No. of Assays done in HEPL')->where('created_date',$get_productivity_outsourcing_list->created_date)->sum('number');
                $outsourced = '';
                $total =$outsourcedpercentge +$heploutsourced;
                $outsourced .= sprintf('%.2f', ($heploutsourced) / ($total) * 100);
                return $outsourced . '';
            })
            ->rawColumns(array("parameter",  "number", "outsourced"))
            ->make(true);
    }
    public function get_productivity_outsourcing_list(Request $req)
    {
        $get_my_productivity_outsourcing = DB::table('tbl_productivity_outsourcing')->where('deleted_status','0');
       
        if( $req->scientistfilter_cl != 'all' && $req->scientistfilter_cl != ''){
        $cl_id = User::where('name', $req->scientistfilter_cl)->pluck('id')->first();
        $tl_id = User::where('supervisor_id', $cl_id)->pluck('id')->toArray();
        $tm_id = User::whereIn('supervisor_id', $tl_id)->pluck('id')->toArray();
        $user_role = User::where('name', $req->scientistfilter_cl)->pluck('role')->first();
        if ($user_role == 5) {
            $mapped_id = array_merge($tl_id, $tm_id);
            $unique_user_id = array_unique($mapped_id);
            $user_id = User::whereIn('supervisor_id', $unique_user_id)->pluck('id')->toArray();
        } else {
            $user_id = array_merge($tl_id, $tm_id);
        }
        $user_id[] = $cl_id;
        $get_my_productivity_outsourcing = $get_my_productivity_outsourcing->whereIn('scientist', $user_id);
        }

        if( $req->scientistfilter_tl != 'all' && $req->scientistfilter_tl != ''){
            $tl_id = User::where('name', $req->scientistfilter_tl)->pluck('id')->first();
            $user_id = User::where('supervisor_id', $tl_id)->pluck('id')->toArray();
            $user_id[] = $tl_id;
            $get_my_productivity_outsourcing = $get_my_productivity_outsourcing->whereIn('scientist', $user_id);
        }
        if( $req->scientistfilter_member != 'all' && $req->scientistfilter_member != ''){
            $user_id = User::where('name', $req->scientistfilter_member)->pluck('id')->first();
            $get_my_productivity_outsourcing = $get_my_productivity_outsourcing->where('scientist', $user_id);
        }
        if(  $req->divisionfilter != ''){
            $stage_gate = DB::table('tbl_divion_master')->where('division_name', $req->divisionfilter)->pluck('id')->first();
            $scient_id = DB::table('users')->where('status', 1)->where('division_id', $stage_gate)->pluck('id')->toArray();
            $get_my_productivity_outsourcing = $get_my_productivity_outsourcing->whereIn('scientist', $scient_id);
        }

        if($req->monthgoal != ''){
            // dd(1);
        $get_my_productivity_outsourcing = $get_my_productivity_outsourcing->where('created_date', $req->monthgoal."-01");
        }
        else
        {
            // dd(2);
            //$get_my_productivity_outsourcing = $get_my_productivity_outsourcing->where('created_date', date('Y-m')."-01");
            $get_my_productivity_outsourcing = $get_my_productivity_outsourcing;
        }

        if (Auth::user()->role == 1) {
            $user_listid1 = User::where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $myteams = DB::table('users')->WhereIn('id', $user_listid)->pluck('id');
            $get_productivity_outsourcing_list = $get_my_productivity_outsourcing
                ->whereIn('scientist', $myteams)
                ->get();
        } else if (Auth::user()->role == 5) {
            // $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            // $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            // $myteams = DB::table('users')->WhereIn('id', $user_listid)->pluck('id');
            $get_productivity_outsourcing_list = $get_my_productivity_outsourcing
        
                // ->whereIn('scientist', $myteams)
                ->whereIn('parameter', ['No.of Formulation in CKPL', 'No.of Formulation Outsourced to HEPL'])

                ->get();
        } else if (Auth::user()->role == 4) {
            // $user_listid1 = User::where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            // $user_listid = User::whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            // $myteams = DB::table('users')->WhereIn('id', $user_listid)->pluck('id');
            $get_productivity_outsourcing_list = $get_my_productivity_outsourcing
            ->whereIn('parameter', ['No.of Formulation in CKPL', 'No.of Formulation Outsourced to HEPL'])
                // ->whereIn('scientist', $myteams)
                ->get();
        } else if (Auth::user()->role == 6) {
            $user_listid1 = User::where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $myteams = DB::table('users')->WhereIn('id', $user_listid)->pluck('id');
           
            $get_productivity_outsourcing_list = $get_my_productivity_outsourcing
            ->whereIn('parameter', ['No.of Formulation in CKPL', 'No.of Formulation Outsourced to HEPL'])
                ->whereIn('scientist', $myteams)
                ->get();
        } else  if (Auth::user()->role == 3) {
            $myteams = DB::table('users')->where('supervisor_id', Auth::id())->orWhere('id', Auth::id())->pluck('id');
            $get_productivity_outsourcing_list = $get_my_productivity_outsourcing
                ->whereIn('scientist', $myteams)
                ->get();
        } else {
            $get_productivity_outsourcing_list = $get_my_productivity_outsourcing
                ->where('scientist', Auth::user()->id)
                ->get();
        }
        //echo '<pre>';print_r($get_productivity_outsourcing_list);exit;
        return Datatables::of($get_productivity_outsourcing_list)
            ->addColumn('parameter', function ($get_productivity_outsourcing_list) {
                $parameter = '';
                $parameter .= $get_productivity_outsourcing_list->parameter;
                return $parameter . '';
            })
            ->addColumn('scientist', function ($get_productivity_outsourcing_list) {
                $scientist = '';
                $user = $get_productivity_outsourcing_list->scientist;
                $user_name = DB::table('users')->where('id', $user)->first();
                $scientist .= $user_name->name;
                return $scientist . '';
            })
            ->addColumn('supervisior_name', function ($get_productivity_outsourcing_list) {
                $supervisior_name = '';               
                $user_name = DB::table('users')->where('id', $get_productivity_outsourcing_list->scientist)->get();
                $supervisior = DB::table('users')->where('id', $user_name[0]->supervisor_id)->first();
                if ($supervisior->id == 68) {
                    $supervisior_name .='-';
                }else{
                    $supervisior_name .= $supervisior->name;
                }
                return $supervisior_name . '';
            })
            ->addColumn('created_date', function ($get_productivity_outsourcing_list) {
                $created_date = '';
                $created_date = $get_productivity_outsourcing_list->created_date;
                return $created_date . '';
            })
            ->addColumn('current_date', function ($get_productivity_outsourcing_list) {
                $create_current_date = '';
                $create_current_date .= date('d-m-Y',strtotime($get_productivity_outsourcing_list->created_current_date)); 
                // $create_current_date .= '';
                // dd($create_current_date);
                return $create_current_date . '';
            })
            
            ->addColumn('number', function ($get_productivity_outsourcing_list) {
                $number = '';
                $number .= $get_productivity_outsourcing_list->number;
                // dd($number);
                // print_r($number);
                return $number . '';
            })
            ->addColumn('division_id', function ($get_productivity_outsourcing_list) {
                $division_name = '';
                $user = $get_productivity_outsourcing_list->scientist;
                $user_name = DB::table('users')->where('id', $user)->first();
                $division_id = $user_name->division_id;
                $user_name = DB::table('tbl_divion_master')->where('id', $division_id)->first();
                $division_name .= $user_name->division_name;
                return $division_name . '';
            })
            ->addColumn('outsourced', function ($get_productivity_outsourcing_list) {

            //    dd($get_productivity_outsourcing_list);
                if(Auth::user()->division_id == 3 || Auth::user()->division_id == 15 ||Auth::user()->division_id == 12 || Auth::user()->division_id == 16)
                { 
                    $outsourcedpercentge =0;
                $ckpldtotal = DB::table('tbl_productivity_outsourcing')->where('deleted_status','0')->where('parameter', 'No. of Assays done in CKPL')->where('scientist', $get_productivity_outsourcing_list->scientist)->where('created_date',$get_productivity_outsourcing_list->created_date)->first('number');
                $heploutsourced = DB::table('tbl_productivity_outsourcing')->where('deleted_status','0')->where('parameter', 'No. of Assays done in HEPL')->where('scientist', $get_productivity_outsourcing_list->scientist)->where('created_date',$get_productivity_outsourcing_list->created_date)->first('number');
                $outsourced = '';
                //dd($heploutsourced->number);
                if($heploutsourced->number != 0 )
                {
                    $outsourcedpercentge = $ckpldtotal->number + $heploutsourced->number;
                  
                    // $outsourced .= sprintf('%.2f', ($heploutsourced) / ($outsourcedpercentge) * 100);

                  
                    $outsourced .= sprintf('%.2f', ( $heploutsourced->number) / ($outsourcedpercentge) * 100);
                }
                else
                {
                    $outsourced .= sprintf('%.2f', 0000);
  
                }

                 }
                else
                {
                    $outsourcedpercentge =0;
                    $ckpldtotal  = DB::table('tbl_productivity_outsourcing')->where('deleted_status','0')->where('parameter', 'No.of Formulation in CKPL')->where('scientist', $get_productivity_outsourcing_list->scientist)->sum('number');
                    $heploutsourced = DB::table('tbl_productivity_outsourcing')->where('deleted_status','0')->where('parameter', 'No.of Formulation Outsourced to HEPL')->where('scientist', $get_productivity_outsourcing_list->scientist)->sum('number');
                   
                    $ckpldtotal1  = DB::table('tbl_productivity_outsourcing')->where('deleted_status','0')->where('parameter', 'No.of Formulation in CKPL')->where('scientist', $get_productivity_outsourcing_list->scientist)->where('created_date',$get_productivity_outsourcing_list->created_date)->orderBy('id', 'DESC')->first();
                    $heploutsourced1 = DB::table('tbl_productivity_outsourcing')->where('deleted_status','0')->where('parameter', 'No.of Formulation Outsourced to HEPL')->where('scientist', $get_productivity_outsourcing_list->scientist)->where('created_date',$get_productivity_outsourcing_list->created_date)->orderBy('id', 'DESC')->first();
                   
                    $outsourced = '';
                    if($heploutsourced != 0  )
                    {
                        // dd($ckpldtotal,$heploutsourced);
                        $outsourcedpercentge = $ckpldtotal1->number + $heploutsourced1->number;
                        $divide=($heploutsourced1->number)/($outsourcedpercentge);
                        // dd($ckpldtotal1->number,$heploutsourced1->number,$outsourcedpercentge);
                        // $outsourced .= sprintf('%.2f', ($heploutsourced) / ($outsourcedpercentge) * 100);
                        $outsourced .= sprintf('%.2f',($heploutsourced1->number) / ($outsourcedpercentge) * 100);
                        // dd($ckpldtotal1->number,$heploutsourced1->number,$outsourcedpercentge,$divide);
                    }
                    else
                    {
                        $outsourced .= sprintf('%.2f', 0000);
      
                    }
                }
                // dd($outsourced);
                return $outsourced . '';
            })
            ->addColumn('action', function ($get_productivity_outsourcing_list) {
                $action = '';
               // $action .= '<td><button class="btn btn-success btn-icon"  onClick=editproductivity(\'' . $get_productivity_outsourcing_list->id . '\')><i class="fa fa-edit"></i></button></td>';
                $action .= '<td><a class="btn btn-success btn-icon" href="#" data-bs-toggle="modal" data-bs-target="#edit_pc_model'.$get_productivity_outsourcing_list->id.'" ><i class="fa fa-edit"></i></a></td>&nbsp;';
                // $action .= '<button class="btn btn-danger btn-icon"  href="#" onClick="delete_productivity_outsourcing('.$get_productivity_outsourcing_list->id.')" ><i class="fa fa-trash"></i></button>&nbsp;';
                return $action . '';
            })
            ->rawColumns(array("parameter", "created_date", "scientist", "division_id", "number", "outsourced","current_date", "action"))
            ->make(true);
    }


   

    public function update_iiy(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'edit_iiy_id' => 'required|integer|exists:tbl_learning_devlopment,id',
            'topics_learnt' => 'required|string|max:255',
            'topics_present' => 'required|string|max:255',
            'hours_invested' => 'required|integer|min:0|max:9999',
            'vendor_facility_visited' => 'required|integer|min:0',
            'conferences_attended' => 'required|integer|min:0',
            'exhibitions_attended' => 'required|integer|min:0',
            'consumer_retail_visits' => 'required|integer|min:0',
            'ideas_generated' => 'required|integer|min:0',
            'date_range_start' => 'required|date',
            'date_range_end' => 'required|date|after_or_equal:date_range_start',
            'uploaded_files.*' => 'file|max:2048', 
            'files_to_delete' => 'array',
            'files_to_delete.*' => 'string', 
        ]);
    
        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }
    
        $existingFiles = json_decode(DB::table('tbl_learning_devlopment')
            ->where('id', $request->edit_iiy_id)
            ->value('upload_the_presentation_weekly'), true) ?? [];
    
        if ($request->has('files_to_delete') && is_array($request->files_to_delete)) {
            foreach ($request->files_to_delete as $fileToDelete) {
                $filePath = "public/presentations/" . $fileToDelete;
                if (Storage::exists($filePath)) {
                    Storage::delete($filePath); 
                }
                $existingFiles = array_values(array_diff($existingFiles, [$fileToDelete])); 
            }
        }
    
        if ($request->hasFile('uploaded_files')) {
            foreach ($request->file('uploaded_files') as $file) {
                $filename = time() . '_' . preg_replace('/\s+/', '_', $file->getClientOriginalName());
                $filePath = 'presentations/' . $filename; 
                $file->storeAs('public/presentations', $filename);
                $existingFiles[] = $filename;
            }
        }
    
        DB::table('tbl_learning_devlopment')
            ->where('id', $request->edit_iiy_id)
            ->update([
                'topics_learnt' => $request->topics_learnt,
                'topics_present' => $request->topics_present,
                'no_of_hours_invested_in_iiy' => $request->hours_invested,
                'no_of_vendor_facility_visited' => $request->vendor_facility_visited,
                'no_of_conference_attended' => $request->conferences_attended,
                'no_of_exhibitions_attended' => $request->exhibitions_attended,
                'no_of_consumer_and_retail_visits' => $request->consumer_retail_visits,
                'no_of_ideas_generated_through_iiy' => $request->ideas_generated,
                'date_range_start' => $request->date_range_start,
                'date_range_end' => $request->date_range_end,
                'upload_the_presentation_weekly' => json_encode(array_values(array_unique($existingFiles))),
                'updated_at' => now(),
            ]);
    
        return response()->json(['success' => true]);
    }
    
    


   
    public function deleteFile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file_name' => 'required|string',
            'edit_iiy_id' => 'required|integer',
        ]);
    
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
    
        $fileName = trim($request->file_name);
        $iiyId = $request->edit_iiy_id;
    
        $existingFiles = DB::table('tbl_learning_devlopment')
            ->where('id', $iiyId)
            ->value('upload_the_presentation_weekly');
    
        $existingFilesArray = $existingFiles ? json_decode($existingFiles, true) : [];
    
      
    
        if (json_last_error() !== JSON_ERROR_NONE) {
            return response()->json(['success' => false, 'message' => 'Invalid file data in database.']);
        }
    
        // Check if filename exists in database (now storing just filenames)
        if (in_array($fileName, $existingFilesArray)) {

            $filePath = 'public/presentations/' . $fileName;
    
            if (Storage::exists($filePath)) {
                Storage::delete($filePath);
            } else {
            }
    
            $updatedFiles = array_diff($existingFilesArray, [$fileName]);
    
            $updateResult = DB::table('tbl_learning_devlopment')
                ->where('id', $iiyId)
                ->update([
                    'upload_the_presentation_weekly' => json_encode(array_values($updatedFiles)),
                    'updated_at' => now(),
                ]);
    
            if ($updateResult) {
                return response()->json(['success' => true, 'message' => 'File deleted successfully.']);
            } else {
                return response()->json(['success' => false, 'message' => 'Database update failed.']);
            }
        }
    
        return response()->json(['success' => false, 'message' => 'File not found in database.']);
    }  
    
public function view_iiy(Request $request) {
        $validator = Validator::make($request->all(), [
            'view_iiy_id' => 'required|integer',
           // 'view_feedback' => 'required',
            'view_rating' => [
                'required',
                function ($attribute, $value, $fail) {
                    if (!preg_match('/^\d+\/\d+$/', $value)) {
                        $fail('The view rating field must be in the format "3/5".');
                    }
                },
            ],
        ]);
    
        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()]);
        }   
        $learningDevelopment = LearningDevelopment::find($request->view_iiy_id);
        if ($learningDevelopment) {
            $learningDevelopment->rating = $request->view_rating;
            $learningDevelopment->feedback = $request->view_feedback;
            $learningDevelopment->save();
        }  
        return response()->json(['success' => true, 'message' => 'Rating added successfully']);
}
    
    

   public function get_insight_details(Request $request)
   {
    // $role = Auth::user()->role;
       $l_dlist = DB::table('tbl_landd_insights')
           ->where('id', $request->id)
           ->where('deleted_status', 0)
        //    ->where('role', $role)
           ->select('*')
           ->get();
       return response()->json($l_dlist);
   }


 public function update_insight(Request $request)
{
    // Validate incoming data
    $validatedData = $request->validate([
        'edit_insight_id' => 'required|integer|exists:tbl_landd_insights,id',
        'edit_insight_topic' => 'required|string',
        'edit_insight_category' => 'nullable|string',
        'edit_scientist_insights' => 'required|string',
        'edit_insight_remarks' => 'nullable|string',
    ]);

    try {
        // Convert scientist field correctly
        $scientistValue = is_numeric($request->edit_scientist_insights)
            ? (int) $request->edit_scientist_insights
            : json_encode(explode(',', $request->edit_scientist_insights));

        // Find and update the insight
        $insight = DB::table('tbl_landd_insights')->where('id', $request->edit_insight_id)->first();

        if (!$insight) {
            return response()->json(['success' => false, 'message' => 'Insight not found.']);
        }

        $updateResult = DB::table('tbl_landd_insights')
            ->where('id', $request->edit_insight_id)
            ->update([
                'insights_generated' => $request->edit_insight_topic,
                'insights_category' => $request->edit_insight_category,
                'scientist' => $scientistValue,
                'remark' => $request->edit_insight_remarks,
                'updated_at' => now(),
            ]);

        if ($updateResult) {
            return response()->json(['success' => true, 'message' => 'Insight updated successfully']);
        } else {
            return response()->json(['success' => false, 'message' => 'No changes detected.']);
        }
    } catch (\Exception $e) {
        return response()->json(['success' => false, 'message' => 'Update failed: ' . $e->getMessage()]);
    }
}

       
    public function get_idea_details(Request $request)
    {
        
        $idea = DB::table('tbl_learning_devlopment_idea')
            ->leftJoin('tbl_landd_insights', function ($join) {
                $join->on(DB::raw('JSON_UNQUOTE(JSON_EXTRACT(tbl_learning_devlopment_idea.idea_insight, "$[0]"))'), '=', 'tbl_landd_insights.id');
            })
            ->select(
                'tbl_learning_devlopment_idea.*',
                'tbl_landd_insights.id as insight_id',
                'tbl_landd_insights.insights_generated as insights_generated'
            )
            ->where('tbl_learning_devlopment_idea.id', $request->id)
            ->first();
    
        if (!$idea) {
            return response()->json(['error' => 'Idea not found'], 404);
        }
        
    
        // Decode scientist IDs
        $scientist_ids = $idea->scientist ? json_decode($idea->scientist, true) : [];
        $scientists = [];
        if (!empty($scientist_ids)) {
            $scientists = DB::table('users')
                ->whereIn('id', $scientist_ids)
                ->select('id', 'name')
                ->get();
        }

        $category_list = $idea->idea_category;
        $categories = '';
        if(!empty($category_list)){
            $categories= DB::table('tbl_learning_devlopment_idea')
            ->select('id','idea_category')
            ->get();
        }
    
        $allInsights = DB::table('tbl_landd_insights')
            ->select('id', 'insights_generated')
            ->get();
    
        $idea->idea_insight = json_decode($idea->idea_insight, true) ?? [];
        $selectedInsights = DB::table('tbl_landd_insights')
            ->whereIn('id', $idea->idea_insight)
            ->pluck('id')
            ->toArray();
    
        $idea->scientists = $scientists;
        $idea->insights = $selectedInsights;
    
    
        return response()->json([
            'idea' => $idea,
            'all_insights' => $allInsights,
            'selected_insights' => $selectedInsights
        ]);
    }
    

    

    public function update_idea(Request $request)
    {
        // Log the request data for debugging
        Log::info('Update idea request data:', $request->all());
        
        $idea = LearningDevelopmentIdea::find($request->edit_idea_id);
        if (!$idea) {
            return response()->json([
                'success' => false,
                'message' => 'Idea not found!'
            ], 404);
        }
    
        $request->validate([
            'edit_idea_id' => 'required|integer',
            'edit_idea_type' => 'required|string', 
            'edit_idea_category' => 'required|string',
            'edit_idea_insight' => 'nullable|array',
            'edit_idea_txt' => 'required|string',
            'edit_idea_scientist' => 'nullable|array',
            'edit_ideas_comments' => 'nullable|string',
            'edit_target_date' => 'nullable|date',
        ]);
    
        // Handle array inputs that might come with [] notation
        $idea_insight = $request->input('edit_idea_insight', []);
        
        $idea_scientist = $request->input('edit_idea_scientist', []);
        
        // Convert single values to arrays if needed
        if (!is_array($idea_insight)) {
            $idea_insight = $idea_insight ? [$idea_insight] : [];
        }
        
        if (!is_array($idea_scientist)) {
            $idea_scientist = $idea_scientist ? [$idea_scientist] : [];
        }
    
        $idea->idea_type = $request->edit_idea_type;
        $idea->idea_category = $request->edit_idea_category;
        $idea->idea_insight = json_encode($idea_insight ?: []);
        $idea->idea_txt = $request->edit_idea_txt;
        $idea->scientist = json_encode($idea_scientist ?: []);
        $idea->ideas_comments = $request->edit_ideas_comments ?: '';
        $idea->target_date = $request->edit_target_date ?: null;
    
        if ($idea->save()) {
            return response()->json([
                'success' => true,
                'message' => 'Idea updated successfully!'
            ], 200);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update the idea. Try again later.'
            ], 500);
        }
    }
    



    
}



